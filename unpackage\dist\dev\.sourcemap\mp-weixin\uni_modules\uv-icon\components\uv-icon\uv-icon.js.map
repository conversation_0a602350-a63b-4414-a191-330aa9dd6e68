{"version": 3, "sources": ["webpack:///D:/project/vt-unih5-order/uni_modules/uv-icon/components/uv-icon/uv-icon.vue?f5d6", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-icon/components/uv-icon/uv-icon.vue?84a4", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-icon/components/uv-icon/uv-icon.vue?a669", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-icon/components/uv-icon/uv-icon.vue?4a0c", "uni-app:///uni_modules/uv-icon/components/uv-icon/uv-icon.vue", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-icon/components/uv-icon/uv-icon.vue?f9fb", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-icon/components/uv-icon/uv-icon.vue?5fd4"], "names": ["name", "emits", "mixins", "data", "colorType", "computed", "uClasses", "classes", "iconStyle", "style", "fontSize", "lineHeight", "fontWeight", "top", "isImg", "imgStyle", "icon", "methods", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7CA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqCtpB;AACA;AAYA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,eA0BA;EACAA;EACAC;EACAC;EACAC;IACA;MACAC,YACA,WACA,WACA,QACA,SACA;IAEA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACA;MACA;MACA;MACA;;MAIA;IACA;IACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACA;QACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAN;MACAA;MACA;IACA;IACA;IACAO;MACA;MACA;MAMA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvJA;AAAA;AAAA;AAAA;AAA6tC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACAjvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uv-icon/components/uv-icon/uv-icon.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uv-icon.vue?vue&type=template&id=646dc59e&scoped=true&\"\nvar renderjs\nimport script from \"./uv-icon.vue?vue&type=script&lang=js&\"\nexport * from \"./uv-icon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uv-icon.vue?vue&type=style&index=0&id=646dc59e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"646dc59e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uv-icon/components/uv-icon/uv-icon.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-icon.vue?vue&type=template&id=646dc59e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.isImg\n    ? _vm.__get_style([_vm.imgStyle, _vm.$uv.addStyle(_vm.customStyle)])\n    : null\n  var s1 = !_vm.isImg\n    ? _vm.__get_style([_vm.iconStyle, _vm.$uv.addStyle(_vm.customStyle)])\n    : null\n  var g0 = _vm.label !== \"\" ? _vm.$uv.addUnit(_vm.labelSize) : null\n  var g1 =\n    _vm.label !== \"\" && _vm.labelPos == \"right\"\n      ? _vm.$uv.addUnit(_vm.space)\n      : null\n  var g2 =\n    _vm.label !== \"\" && _vm.labelPos == \"bottom\"\n      ? _vm.$uv.addUnit(_vm.space)\n      : null\n  var g3 =\n    _vm.label !== \"\" && _vm.labelPos == \"left\"\n      ? _vm.$uv.addUnit(_vm.space)\n      : null\n  var g4 =\n    _vm.label !== \"\" && _vm.labelPos == \"top\"\n      ? _vm.$uv.addUnit(_vm.space)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-icon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-icon.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t  class=\"uv-icon\"\r\n\t  @tap=\"clickHandler\"\r\n\t  :class=\"['uv-icon--' + labelPos]\"\r\n\t>\r\n\t\t<image\r\n\t\t  class=\"uv-icon__img\"\r\n\t\t  v-if=\"isImg\"\r\n\t\t  :src=\"name\"\r\n\t\t  :mode=\"imgMode\"\r\n\t\t  :style=\"[imgStyle, $uv.addStyle(customStyle)]\"\r\n\t\t></image>\r\n\t\t<text\r\n\t\t  v-else\r\n\t\t  class=\"uv-icon__icon\"\r\n\t\t  :class=\"uClasses\"\r\n\t\t  :style=\"[iconStyle, $uv.addStyle(customStyle)]\"\r\n\t\t  :hover-class=\"hoverClass\"\r\n\t\t>{{icon}}</text>\r\n\t\t<!-- 这里进行空字符串判断，如果仅仅是v-if=\"label\"，可能会出现传递0的时候，结果也无法显示 -->\r\n\t\t<text\r\n\t\t  v-if=\"label !== ''\" \r\n\t\t  class=\"uv-icon__label\"\r\n\t\t  :style=\"{\r\n\t\t\tcolor: labelColor,\r\n\t\t\tfontSize: $uv.addUnit(labelSize),\r\n\t\t\tmarginLeft: labelPos == 'right' ? $uv.addUnit(space) : 0,\r\n\t\t\tmarginTop: labelPos == 'bottom' ? $uv.addUnit(space) : 0,\r\n\t\t\tmarginRight: labelPos == 'left' ? $uv.addUnit(space) : 0,\r\n\t\t\tmarginBottom: labelPos == 'top' ? $uv.addUnit(space) : 0\r\n\t\t}\"\r\n\t\t>{{ label }}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport mpMixin from '@/uni_modules/uv-ui-tools/libs/mixin/mpMixin.js'\r\n\timport mixin from '@/uni_modules/uv-ui-tools/libs/mixin/mixin.js'\r\n\t// #ifdef APP-NVUE\r\n\t// nvue通过weex的dom模块引入字体，相关文档地址如下：\r\n\t// https://weex.apache.org/zh/docs/modules/dom.html#addrule\r\n\timport iconUrl from './uvicons.ttf';\r\n\tconst domModule = weex.requireModule('dom')\r\n\tdomModule.addRule('fontFace', {\r\n\t\t'fontFamily': \"uvicon-iconfont\",\r\n\t\t'src': \"url('\" + iconUrl + \"')\"\r\n\t})\r\n\t// #endif\r\n\t// 引入图标名称，已经对应的unicode\r\n\timport icons from './icons';\r\n\timport props from './props.js';\r\n\t/**\r\n\t * icon 图标\r\n\t * @description 基于字体的图标集，包含了大多数常见场景的图标。\r\n\t * @tutorial https://www.uvui.cn/components/icon.html\r\n\t * @property {String}\t\t\tname\t\t\t图标名称，见示例图标集\r\n\t * @property {String}\t\t\tcolor\t\t\t图标颜色,可接受主题色 （默认 color['uv-content-color'] ）\r\n\t * @property {String | Number}\tsize\t\t\t图标字体大小，单位px （默认 '16px' ）\r\n\t * @property {Boolean}\t\t\tbold\t\t\t是否显示粗体 （默认 false ）\r\n\t * @property {String | Number}\tindex\t\t\t点击图标的时候传递事件出去的index（用于区分点击了哪一个）\r\n\t * @property {String}\t\t\thoverClass\t\t图标按下去的样式类，用法同uni的view组件的hoverClass参数，详情见官网\r\n\t * @property {String}\t\t\tcustomPrefix\t自定义扩展前缀，方便用户扩展自己的图标库 （默认 'uicon' ）\r\n\t * @property {String | Number}\tlabel\t\t\t图标右侧的label文字\r\n\t * @property {String}\t\t\tlabelPos\t\tlabel相对于图标的位置，只能right或bottom （默认 'right' ）\r\n\t * @property {String | Number}\tlabelSize\t\tlabel字体大小，单位px （默认 '15px' ）\r\n\t * @property {String}\t\t\tlabelColor\t\t图标右侧的label文字颜色 （ 默认 color['uv-content-color'] ）\r\n\t * @property {String | Number}\tspace\t\t\tlabel与图标的距离，单位px （默认 '3px' ）\r\n\t * @property {String}\t\t\timgMode\t\t\t图片的mode\r\n\t * @property {String | Number}\twidth\t\t\t显示图片小图标时的宽度\r\n\t * @property {String | Number}\theight\t\t\t显示图片小图标时的高度\r\n\t * @property {String | Number}\ttop\t\t\t\t图标在垂直方向上的定位 用于解决某些情况下，让图标垂直居中的用途  （默认 0 ）\r\n\t * @property {Boolean}\t\t\tstop\t\t\t是否阻止事件传播 （默认 false ）\r\n\t * @property {Object}\t\t\tcustomStyle\t\ticon的样式，对象形式\r\n\t * @event {Function} click 点击图标时触发\r\n\t * @event {Function} touchstart 事件触摸时触发\r\n\t * @example <uv-icon name=\"photo\" color=\"#2979ff\" size=\"28\"></uv-icon>\r\n\t */\r\n\texport default {\r\n\t\tname: 'uv-icon',\r\n\t\temits: ['click'],\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcolorType: [\r\n\t\t\t\t\t'primary',\r\n\t\t\t\t\t'success',\r\n\t\t\t\t\t'info',\r\n\t\t\t\t\t'error',\r\n\t\t\t\t\t'warning'\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tuClasses() {\r\n\t\t\t\tlet classes = []\r\n\t\t\t\tclasses.push(this.customPrefix)\r\n\t\t\t\tclasses.push(this.customPrefix + '-' + this.name)\r\n\t\t\t\t// 主题色，通过类配置\r\n\t\t\t\tif (this.color && this.colorType.includes(this.color)) classes.push('uv-icon__icon--' + this.color)\r\n\t\t\t\t// 阿里，头条，百度小程序通过数组绑定类名时，无法直接使用[a, b, c]的形式，否则无法识别\r\n\t\t\t\t// 故需将其拆成一个字符串的形式，通过空格隔开各个类名\r\n\t\t\t\t//#ifdef MP-ALIPAY || MP-TOUTIAO || MP-BAIDU\r\n\t\t\t\tclasses = classes.join(' ')\r\n\t\t\t\t//#endif\r\n\t\t\t\treturn classes\r\n\t\t\t},\r\n\t\t\ticonStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\tstyle = {\r\n\t\t\t\t\tfontSize: this.$uv.addUnit(this.size),\r\n\t\t\t\t\tlineHeight: this.$uv.addUnit(this.size),\r\n\t\t\t\t\tfontWeight: this.bold ? 'bold' : 'normal',\r\n\t\t\t\t\t// 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中\r\n\t\t\t\t\ttop: this.$uv.addUnit(this.top)\r\n\t\t\t\t}\r\n\t\t\t\t// 非主题色值时，才当作颜色值\r\n\t\t\t\tif (this.color && !this.colorType.includes(this.color)) style.color = this.color\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\r\n\t\t\tisImg() {\r\n\t\t\t\tconst isBase64 = this.name.indexOf('data:') > -1 && this.name.indexOf('base64') > -1;\r\n\t\t\t\treturn this.name.indexOf('/') !== -1 || isBase64;\r\n\t\t\t},\r\n\t\t\timgStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\t// 如果设置width和height属性，则优先使用，否则使用size属性\r\n\t\t\t\tstyle.width = this.width ? this.$uv.addUnit(this.width) : this.$uv.addUnit(this.size)\r\n\t\t\t\tstyle.height = this.height ? this.$uv.addUnit(this.height) : this.$uv.addUnit(this.size)\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\t// 通过图标名，查找对应的图标\r\n\t\t\ticon() {\r\n\t\t\t\t// 如果内置的图标中找不到对应的图标，就直接返回name值，因为用户可能传入的是unicode代码\r\n\t\t\t\tconst code = icons['uvicon-' + this.name];\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tif(!code) {\r\n\t\t\t\t\treturn code ? unescape(`%u${code}`) : ['uvicon'].indexOf(this.customPrefix) > -1 ? unescape(`%u${this.name}`) : '';\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn code ? unescape(`%u${code}`) : ['uvicon'].indexOf(this.customPrefix) > -1 ? this.name : '';\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tclickHandler(e) {\r\n\t\t\t\tthis.$emit('click', this.index)\r\n\t\t\t\t// 是否阻止事件冒泡\r\n\t\t\t\tthis.stop && this.preventEvent(e)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/components.scss';\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/color.scss';\r\n\t// 变量定义\r\n\t$uv-icon-primary: $uv-primary !default;\r\n\t$uv-icon-success: $uv-success !default;\r\n\t$uv-icon-info: $uv-info !default;\r\n\t$uv-icon-warning: $uv-warning !default;\r\n\t$uv-icon-error: $uv-error !default;\r\n\t$uv-icon-label-line-height: 1 !default;\r\n\t/* #ifndef APP-NVUE */\r\n\t// 非nvue下加载字体\r\n\t@font-face {\r\n\t\tfont-family: 'uvicon-iconfont';\r\n\t\tsrc: url('./uvicons.ttf') format('truetype');\r\n\t}\r\n\t/* #endif */\r\n\t.uv-icon {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\talign-items: center;\r\n\t\t&--left {\r\n\t\t\tflex-direction: row-reverse;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t\t&--right {\r\n\t\t\tflex-direction: row;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t\t&--top {\r\n\t\t\tflex-direction: column-reverse;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\t\t&--bottom {\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\t\t&__icon {\r\n\t\t\tfont-family: uvicon-iconfont;\r\n\t\t\tposition: relative;\r\n\t\t\t@include flex;\r\n\t\t\talign-items: center;\r\n\t\t\t&--primary {\r\n\t\t\t\tcolor: $uv-icon-primary;\r\n\t\t\t}\r\n\t\t\t&--success {\r\n\t\t\t\tcolor: $uv-icon-success;\r\n\t\t\t}\r\n\t\t\t&--error {\r\n\t\t\t\tcolor: $uv-icon-error;\r\n\t\t\t}\r\n\t\t\t&--warning {\r\n\t\t\t\tcolor: $uv-icon-warning;\r\n\t\t\t}\r\n\t\t\t&--info {\r\n\t\t\t\tcolor: $uv-icon-info;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&__img {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\theight: auto;\r\n\t\t\twill-change: transform;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\t\t&__label {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tline-height: $uv-icon-label-line-height;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-icon.vue?vue&type=style&index=0&id=646dc59e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-icon.vue?vue&type=style&index=0&id=646dc59e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759141818824\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}