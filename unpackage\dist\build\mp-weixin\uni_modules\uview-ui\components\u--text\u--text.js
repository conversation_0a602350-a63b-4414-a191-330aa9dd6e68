(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u--text/u--text"],{"0d1d":function(n,e,t){"use strict";t.d(e,"b",(function(){return u})),t.d(e,"c",(function(){return i})),t.d(e,"a",(function(){}));var u=function(){var n=this.$createElement;this._self._c},i=[]},"258e":function(n,e,t){"use strict";t.r(e);var u=t("a907"),i=t.n(u);for(var o in u)["default"].indexOf(o)<0&&function(n){t.d(e,n,(function(){return u[n]}))}(o);e["default"]=i.a},a907:function(n,e,t){"use strict";(function(n){var u=t("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=u(t("814f")),o={name:"u--text",mixins:[n.$u.mpMixin,i.default,n.$u.mixin],components:{uvText:function(){Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-text/u-text")]).then(function(){return resolve(t("ae2c"))}.bind(null,t)).catch(t.oe)}}};e.default=o}).call(this,t("df3c")["default"])},e153:function(n,e,t){"use strict";t.r(e);var u=t("0d1d"),i=t("258e");for(var o in i)["default"].indexOf(o)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(o);var a=t("828b"),c=Object(a["a"])(i["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);e["default"]=c.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u--text/u--text-create-component',
    {
        'uni_modules/uview-ui/components/u--text/u--text-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("e153"))
        })
    },
    [['uni_modules/uview-ui/components/u--text/u--text-create-component']]
]);
