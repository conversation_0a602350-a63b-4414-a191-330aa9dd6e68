import http from '@/http/api.js'

// 获取业务列表
const getWorkerOrder = (params) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/externalPage',
		method: 'get',
        params
	})
}
const signIn = (data) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/sign',
		method: 'POST',
        data
	})
}
const startWorkerOrder = (id) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/acceptOrder',
		method: 'POST',
        params: {id}
	})
}

const finishWorkerOrder = (data) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/completeOrder',
		method: 'POST',
		data
	})
}
const getWorkerOrderDetail = (id) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/detail',
		method: 'get',
        params: {id}
	})
}
//  申请结算
const applySettlement = (data) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/savePayment',
		method: 'POST',
        data
	})
}
// 修改结算
const editSettlement = (data) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/updatePayment',
		method: 'POST',
        data
	})
}
// 获取工单统计
const getTaskStatistics = (params) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/externalPageStatistics',
		method: 'get',
        params
	})
}

// 获取结算申请分页
const getSettlementApply = (params) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/pageForPayment',
		method: 'get',
        params
	})
}
// 获取结算统计
const getSettlementStatistics = (params) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/pageForPaymentStatistics',
		method: 'get',
        params
	})
}
export default {
	getWorkerOrder,
    startWorkerOrder,
	finishWorkerOrder,
	signIn,
    getWorkerOrderDetail,
	applySettlement,
	getTaskStatistics,getSettlementApply,editSettlement,getSettlementStatistics
}