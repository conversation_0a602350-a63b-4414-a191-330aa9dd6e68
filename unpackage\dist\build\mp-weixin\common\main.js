(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/main"],{"0cb3":function(e,t,n){"use strict";(function(e,t,o){var c=n("47a9"),r=c(n("7ca3"));n("ea4a");var u=c(n("3240")),a=c(n("361b")),f=c(n("8f63")),i=c(n("4681")),l=c(n("86f7"));function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}e.__webpack_require_UNI_MP_PLUGIN__=n,u.default.config.productionTip=!1,u.default.use(f.default),a.default.mpType="app";var p=new u.default(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){(0,r.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},a.default));u.default.use(l.default,p),u.default.use(i.default,p),t(p).$mount();var s=o.getUpdateManager();s.onCheckForUpdate((function(e){console.log(e.hasUpdate,"版本是否有更新")})),s.onUpdateReady((function(e){o.showModal({title:"更新提示",content:"新版本已经准备好，是否重启应用？",success:function(e){e.confirm&&s.applyUpdate()}})})),s.onUpdateFailed((function(e){}))}).call(this,n("3223")["default"],n("df3c")["createApp"],n("df3c")["default"])},"345c":function(e,t,n){"use strict";n.r(t);var o=n("9e70"),c=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=c.a},"361b":function(e,t,n){"use strict";n.r(t);var o=n("345c");for(var c in o)["default"].indexOf(c)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(c);n("748c6");var r=n("828b"),u=Object(r["a"])(o["default"],void 0,void 0,!1,null,null,null,!1,void 0,void 0);t["default"]=u.exports},"4b0c":function(e,t,n){},"748c6":function(e,t,n){"use strict";var o=n("4b0c"),c=n.n(o);c.a},"9e70":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={onLaunch:function(){console.warn("当前组件仅支持 uni_modules 目录结构 ，请升级 HBuilderX 到 3.1.0 版本以上！"),console.log("App Launch")},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}};t.default=o}},[["0cb3","common/runtime","common/vendor"]]]);