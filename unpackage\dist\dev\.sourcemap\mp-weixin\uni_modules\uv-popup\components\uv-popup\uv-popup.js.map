{"version": 3, "sources": ["webpack:///D:/project/vt-unih5-order/uni_modules/uv-popup/components/uv-popup/uv-popup.vue?9a40", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-popup/components/uv-popup/uv-popup.vue?cf50", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-popup/components/uv-popup/uv-popup.vue?972c", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-popup/components/uv-popup/uv-popup.vue?a048", "uni-app:///uni_modules/uv-popup/components/uv-popup/uv-popup.vue", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-popup/components/uv-popup/uv-popup.vue?cae9", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-popup/components/uv-popup/uv-popup.vue?7410"], "names": ["name", "components", "mixins", "emits", "props", "mode", "type", "default", "duration", "zIndex", "bgColor", "safeArea", "overlay", "closeOnClickOverlay", "overlayOpacity", "overlayStyle", "safeAreaInsetBottom", "safeAreaInsetTop", "closeable", "closeIconPos", "zoom", "round", "uni", "watch", "handler", "immediate", "isDesktop", "showPopup", "data", "ani", "showTrans", "popup<PERSON><PERSON><PERSON>", "popupHeight", "config", "top", "bottom", "center", "left", "right", "message", "dialog", "share", "transitionStyle", "position", "maskShow", "mkclick", "popupClass", "direction", "computed", "bg", "contentStyle", "style", "destroyed", "created", "methods", "setH5Visible", "closeMask", "clear", "e", "open", "show", "close", "clearTimeout", "touchstart", "onTap", "backgroundColor", "display", "flexDirection", "justifyContent", "alignItems"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAmoB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACiEvpB;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,eA0BA;EACAA;EACAC,aAIA;EACAC;EACAC;EACAC;IACA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MAKAC;IAEA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;EAAA,eACAe,4JACA;EACAC;IACA;AACA;AACA;IACAjB;MACAkB;QACA;QACA;MACA;MACAC;IACA;IACAC;MACAF;QACA;QACA;MACA;MACAC;IACA;IACA;IACAE,qCAKA;EACA;EACAC;IACA;MACAC;MACAF;MACAG;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAN;QACAC;MACA;MACAM;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAtB;MACA;IACA;IACAuB;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACA;QACA;QACA;QACAA;QACA;UACAA;UACAA;QACA;UACAA;UACAA;QACA;UACAA;QACA;MACA;MACA;IACA;EACA;EAEA;EACAC;IACA;EACA;EAQAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC,uCAKA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;IACAC;MAEAC;MAEA;IACA;IAEAC;MACA;MACA;QACA;MACA;MACA;MACA;QACAZ;MACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;QACAa;QACAtD;MACA;IACA;IACAuD;MAAA;MACA;MACA;QACAD;QACAtD;MACA;MACAwD;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACA9B;MAAA;MACA;MACA;MACA;QACAS;QACAlC;QACA4B;QACAC;QACA2B;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACA9B;MACA;MACA;MACA;QACAQ;QACAlC;QACA4B;QACAC;QACAH;QACA8B;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACA7B;MACA;MACA;MACA;QACAO;QACAlC;QAEAyD;QACAC;QAEAhC;QACAE;QACAC;QACAJ;QACAkC;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAhC;MACA;MACA;MACA;QACAM;QACAlC;QACA4B;QACAF;QACAD;QACA+B;QAEAC;QACAC;MAEA;MACA;MACA;MACA;MACA;IACA;IACA7B;MACA;MACA;MACA;QACAK;QACAlC;QACA0B;QACAG;QACAJ;QACA+B;QAEAC;QACAC;MAEA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9cA;AAAA;AAAA;AAAA;AAA8tC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAlvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uv-popup/components/uv-popup/uv-popup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uv-popup.vue?vue&type=template&id=4cc3c370&scoped=true&\"\nvar renderjs\nimport script from \"./uv-popup.vue?vue&type=script&lang=js&\"\nexport * from \"./uv-popup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uv-popup.vue?vue&type=style&index=0&id=4cc3c370&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4cc3c370\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uv-popup/components/uv-popup/uv-popup.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-popup.vue?vue&type=template&id=4cc3c370&scoped=true&\"", "var components\ntry {\n  components = {\n    uvOverlay: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-overlay/components/uv-overlay/uv-overlay\" */ \"@/uni_modules/uv-overlay/components/uv-overlay/uv-overlay.vue\"\n      )\n    },\n    uvTransition: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-transition/components/uv-transition/uv-transition\" */ \"@/uni_modules/uv-transition/components/uv-transition/uv-transition.vue\"\n      )\n    },\n    uvStatusBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar\" */ \"@/uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar.vue\"\n      )\n    },\n    uvSafeBottom: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-safe-bottom/components/uv-safe-bottom/uv-safe-bottom\" */ \"@/uni_modules/uv-safe-bottom/components/uv-safe-bottom/uv-safe-bottom.vue\"\n      )\n    },\n    uvIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-icon/components/uv-icon/uv-icon\" */ \"@/uni_modules/uv-icon/components/uv-icon/uv-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.showPopup ? _vm.__get_style([_vm.contentStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-popup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-popup.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view \r\n\t\tv-if=\"showPopup\" \r\n\t\tclass=\"uv-popup\" \r\n\t\t:class=\"[popupClass, isDesktop ? 'fixforpc-z-index' : '']\"\r\n\t\t:style=\"[{zIndex: zIndex}]\"\r\n\t>\r\n\t\t<view @touchstart=\"touchstart\">\r\n\t\t\t<!-- 遮罩层 -->\r\n\t\t\t<uv-overlay\r\n\t\t\t\tkey=\"1\"\r\n\t\t\t\tv-if=\"maskShow && overlay\"\r\n\t\t\t\t:show=\"showTrans\"\r\n\t\t\t\t:duration=\"duration\"\r\n\t\t\t\t:custom-style=\"overlayStyle\"\r\n\t\t\t\t:opacity=\"overlayOpacity\"\r\n\t\t\t  :zIndex=\"zIndex\"\r\n\t\t\t\t@click=\"onTap\"\r\n\t\t\t></uv-overlay>\r\n\t\t\t<uv-transition \r\n\t\t\t\tkey=\"2\" \r\n\t\t\t\t:mode=\"ani\" \r\n\t\t\t\tname=\"content\" \r\n\t\t\t\t:custom-style=\"transitionStyle\" \r\n\t\t\t\t:duration=\"duration\"\r\n\t\t\t\t:show=\"showTrans\" \r\n\t\t\t\t@click=\"onTap\"\r\n\t\t\t>\r\n\t\t\t\t<view \r\n\t\t\t\t\tclass=\"uv-popup__content\" \r\n\t\t\t\t\t:style=\"[contentStyle]\" \r\n\t\t\t\t\t:class=\"[popupClass]\" \r\n\t\t\t\t\t@click=\"clear\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<uv-status-bar v-if=\"safeAreaInsetTop\"></uv-status-bar>\r\n\t\t\t\t\t<slot />\r\n\t\t\t\t\t<uv-safe-bottom v-if=\"safeAreaInsetBottom\"></uv-safe-bottom>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-if=\"closeable\"\r\n\t\t\t\t\t\**********=\"close\"\r\n\t\t\t\t\t\tclass=\"uv-popup__content__close\"\r\n\t\t\t\t\t\t:class=\"['uv-popup__content__close--' + closeIconPos]\"\r\n\t\t\t\t\t\thover-class=\"uv-popup__content__close--hover\"\r\n\t\t\t\t\t\thover-stay-time=\"150\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<uv-icon\r\n\t\t\t\t\t\t\tname=\"close\"\r\n\t\t\t\t\t\t\tcolor=\"#909399\"\r\n\t\t\t\t\t\t\tsize=\"18\"\r\n\t\t\t\t\t\t\tbold\r\n\t\t\t\t\t\t></uv-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uv-transition>\r\n\t\t</view>\r\n\t\t<!-- #ifdef H5 -->\r\n\t\t<keypress v-if=\"maskShow\" @esc=\"onTap\" />\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef H5\r\n\timport keypress from './keypress.js'\r\n\t// #endif\r\n\timport mpMixin from '@/uni_modules/uv-ui-tools/libs/mixin/mpMixin.js'\r\n\timport mixin from '@/uni_modules/uv-ui-tools/libs/mixin/mixin.js'\r\n\t/**\r\n\t* PopUp 弹出层\r\n\t* @description 弹出层组件，为了解决遮罩弹层的问题\r\n\t* @tutorial https://www.uvui.cn/components/popup.html\r\n\t* @property {String} mode = [top|center|bottom|left|right] 弹出方式\r\n\t* \t@value top 顶部弹出\r\n\t* \t@value center 中间弹出\r\n\t* \t@value bottom 底部弹出\r\n\t* \t@value left\t\t左侧弹出\r\n\t* \t@value right  右侧弹出\r\n\t* @property {Number} duration 动画时长，默认300\r\n\t* @property {Boolean} overlay 是否显示遮罩，默认true\r\n\t* @property {Boolean} overlayOpacity 遮罩透明度，默认0.5 \r\n\t* @property {Object} overlayStyle 遮罩自定义样式\r\n\t* @property {Boolean} closeOnClickOverlay = [true|false] 蒙版点击是否关闭弹窗，默认true\r\n\t* @property {Number | String} zIndex 弹出层的层级\r\n\t* @property {Boolean} safeAreaInsetTop 是否留出顶部安全区（状态栏高度），默认false\r\n\t* @property {Boolean} safeAreaInsetBottom 是否为留出底部安全区适配，默认true\r\n\t* @property {Boolean} closeable 是否显示关闭图标，默认false\r\n\t* @property {Boolean} closeIconPos 自定义关闭图标位置，`top-left`-左上角，`top-right`-右上角，`bottom-left`-左下角，`bottom-right`-右下角，默认top-right\r\n\t* @property {String}  bgColor 主窗口背景色\r\n\t* @property {String}  maskBackgroundColor 蒙版颜色\r\n\t* @property {Boolean} customStyle 自定义样式\r\n\t* @event {Function} change 打开关闭弹窗触发，e={show: false}\r\n\t* @event {Function} maskClick 点击遮罩触发\r\n\t*/\r\n\texport default {\r\n\t\tname: 'uv-popup',\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef H5\r\n\t\t\tkeypress\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmixins: [mpMixin, mixin],\r\n\t\temits: ['change', 'maskClick'],\r\n\t\tprops: {\r\n\t\t\t// 弹出层类型，可选值，top: 顶部弹出层；bottom：底部弹出层；center：全屏弹出层\r\n\t\t\t// message: 消息提示 ; dialog : 对话框\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'center'\r\n\t\t\t},\r\n\t\t\t// 动画时长，单位ms\r\n\t\t\tduration: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 300\r\n\t\t\t},\r\n\t\t\t// 层级\r\n\t\t\tzIndex: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tdefault: 997\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tdefault: 10075\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#ffffff'\r\n\t\t\t},\r\n\t\t\tsafeArea: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 是否显示遮罩\r\n\t\t\toverlay: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 点击遮罩是否关闭弹窗\r\n\t\t\tcloseOnClickOverlay: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 遮罩的透明度，0-1之间\r\n\t\t\toverlayOpacity: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0.4\r\n\t\t\t},\r\n\t\t\t// 自定义遮罩的样式\r\n\t\t\toverlayStyle: {\r\n\t\t\t\ttype: [Object, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 是否为iPhoneX留出底部安全距离\r\n\t\t\tsafeAreaInsetBottom: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 是否留出顶部安全距离（状态栏高度）\r\n\t\t\tsafeAreaInsetTop: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 是否显示关闭图标\r\n\t\t\tcloseable: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 自定义关闭图标位置，top-left为左上角，top-right为右上角，bottom-left为左下角，bottom-right为右下角\r\n\t\t\tcloseIconPos: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'top-right'\r\n\t\t\t},\r\n\t\t\t// mode=center，也即中部弹出时，是否使用缩放模式\r\n\t\t\tzoom: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tround: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t...uni.$uv?.props?.popup\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t/**\r\n\t\t\t * 监听type类型\r\n\t\t\t */\r\n\t\t\ttype: {\r\n\t\t\t\thandler: function(type) {\r\n\t\t\t\t\tif (!this.config[type]) return\r\n\t\t\t\t\tthis[this.config[type]](true)\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\tisDesktop: {\r\n\t\t\t\thandler: function(newVal) {\r\n\t\t\t\t\tif (!this.config[newVal]) return\r\n\t\t\t\t\tthis[this.config[this.mode]](true)\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\t// H5 下禁止底部滚动\r\n\t\t\tshowPopup(show) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// fix by mehaotian 处理 h5 滚动穿透的问题\r\n\t\t\t\tdocument.getElementsByTagName('body')[0].style.overflow = show ? 'hidden' : 'visible'\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tani: [],\r\n\t\t\t\tshowPopup: false,\r\n\t\t\t\tshowTrans: false,\r\n\t\t\t\tpopupWidth: 0,\r\n\t\t\t\tpopupHeight: 0,\r\n\t\t\t\tconfig: {\r\n\t\t\t\t\ttop: 'top',\r\n\t\t\t\t\tbottom: 'bottom',\r\n\t\t\t\t\tcenter: 'center',\r\n\t\t\t\t\tleft: 'left',\r\n\t\t\t\t\tright: 'right',\r\n\t\t\t\t\tmessage: 'top',\r\n\t\t\t\t\tdialog: 'center',\r\n\t\t\t\t\tshare: 'bottom'\r\n\t\t\t\t},\r\n\t\t\t\ttransitionStyle: {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0\r\n\t\t\t\t},\r\n\t\t\t\tmaskShow: true,\r\n\t\t\t\tmkclick: true,\r\n\t\t\t\tpopupClass: this.isDesktop ? 'fixforpc-top' : 'top',\r\n\t\t\t\tdirection: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tisDesktop() {\r\n\t\t\t\treturn this.popupWidth >= 500 && this.popupHeight >= 500\r\n\t\t\t},\r\n\t\t\tbg() {\r\n\t\t\t\tif (this.bgColor === '' || this.bgColor === 'none' || this.$uv.getPx(this.round)>0) {\r\n\t\t\t\t\treturn 'transparent'\r\n\t\t\t\t}\r\n\t\t\t\treturn this.bgColor\r\n\t\t\t},\r\n\t\t\tcontentStyle() {\r\n\t\t\t\tconst style = {};\r\n\t\t\t\tif (this.bgColor) {\r\n\t\t\t\t\tstyle.backgroundColor = this.bg\r\n\t\t\t\t}\r\n\t\t\t\tif(this.round) {\r\n\t\t\t\t\tconst value = this.$uv.addUnit(this.round)\r\n\t\t\t\t\tconst mode = this.direction?this.direction:this.mode\r\n\t\t\t\t\tstyle.backgroundColor = this.bgColor\r\n\t\t\t\t\tif(mode === 'top') {\r\n\t\t\t\t\t\tstyle.borderBottomLeftRadius = value\r\n\t\t\t\t\t\tstyle.borderBottomRightRadius = value\r\n\t\t\t\t\t} else if(mode === 'bottom') {\r\n\t\t\t\t\t\tstyle.borderTopLeftRadius = value\r\n\t\t\t\t\t\tstyle.borderTopRightRadius = value\r\n\t\t\t\t\t} else if(mode === 'center') {\r\n\t\t\t\t\t\tstyle.borderRadius = value\r\n\t\t\t\t\t} \r\n\t\t\t\t}\r\n\t\t\t\treturn this.$uv.deepMerge(style, this.$uv.addStyle(this.customStyle))\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifndef VUE3\r\n\t\t// TODO vue2\r\n\t\tdestroyed() {\r\n\t\t\tthis.setH5Visible()\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE3\r\n\t\t// TODO vue3\r\n\t\tunmounted() {\r\n\t\t\tthis.setH5Visible()\r\n\t\t},\r\n\t\t// #endif\r\n\t\tcreated() {\r\n\t\t\t// TODO 处理 message 组件生命周期异常的问题\r\n\t\t\tthis.messageChild = null\r\n\t\t\t// TODO 解决头条冒泡的问题\r\n\t\t\tthis.clearPropagation = false\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetH5Visible() {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// fix by mehaotian 处理 h5 滚动穿透的问题\r\n\t\t\t\tdocument.getElementsByTagName('body')[0].style.overflow = 'visible'\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 公用方法，不显示遮罩层\r\n\t\t\t */\r\n\t\t\tcloseMask() {\r\n\t\t\t\tthis.maskShow = false\r\n\t\t\t},\r\n\t\t\t// TODO nvue 取消冒泡\r\n\t\t\tclear(e) {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\te.stopPropagation()\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.clearPropagation = true\r\n\t\t\t},\r\n\r\n\t\t\topen(direction) {\r\n\t\t\t\t// fix by mehaotian 处理快速打开关闭的情况\r\n\t\t\t\tif (this.showPopup) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet innerType = ['top', 'center', 'bottom', 'left', 'right', 'message', 'dialog', 'share']\r\n\t\t\t\tif (!(direction && innerType.indexOf(direction) !== -1)) {\r\n\t\t\t\t\tdirection = this.mode\r\n\t\t\t\t}else {\r\n\t\t\t\t\tthis.direction = direction;\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.config[direction]) {\r\n\t\t\t\t\treturn this.$uv.error(`缺少类型：${direction}`);\r\n\t\t\t\t}\r\n\t\t\t\tthis[this.config[direction]]()\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\tshow: true,\r\n\t\t\t\t\ttype: direction\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclose(type) {\r\n\t\t\t\tthis.showTrans = false\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\tshow: false,\r\n\t\t\t\t\ttype: this.mode\r\n\t\t\t\t})\r\n\t\t\t\tclearTimeout(this.timer)\r\n\t\t\t\t// // 自定义关闭事件\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis.showPopup = false\r\n\t\t\t\t}, 300)\r\n\t\t\t},\r\n\t\t\t// TODO 处理冒泡事件，头条的冒泡事件有问题 ，先这样兼容\r\n\t\t\ttouchstart() {\r\n\t\t\t\tthis.clearPropagation = false\r\n\t\t\t},\r\n\t\t\tonTap() {\r\n\t\t\t\tif (this.clearPropagation) {\r\n\t\t\t\t\t// fix by mehaotian 兼容 nvue\r\n\t\t\t\t\tthis.clearPropagation = false\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('maskClick')\r\n\t\t\t\tif (!this.closeOnClickOverlay) return\r\n\t\t\t\tthis.close()\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 顶部弹出样式处理\r\n\t\t\t */\r\n\t\t\ttop(type) {\r\n\t\t\t\tthis.popupClass = this.isDesktop ? 'fixforpc-top' : 'top'\r\n\t\t\t\tthis.ani = ['slide-top']\r\n\t\t\t\tthis.transitionStyle = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tzIndex: this.zIndex,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\tbackgroundColor: this.bg\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tif (this.messageChild && this.mode === 'message') {\r\n\t\t\t\t\t\tthis.messageChild.timerClose()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 底部弹出样式处理\r\n\t\t\t */\r\n\t\t\tbottom(type) {\r\n\t\t\t\tthis.popupClass = 'bottom'\r\n\t\t\t\tthis.ani = ['slide-bottom']\r\n\t\t\t\tthis.transitionStyle = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tzIndex: this.zIndex,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\tbackgroundColor: this.bg\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 中间弹出样式处理\r\n\t\t\t */\r\n\t\t\tcenter(type) {\r\n\t\t\t\tthis.popupClass = 'center'\r\n\t\t\t\tthis.ani = this.zoom?['zoom-in', 'fade']:['fade'];\r\n\t\t\t\tthis.transitionStyle = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tzIndex: this.zIndex,\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\tflexDirection: 'column',\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tjustifyContent: 'center',\r\n\t\t\t\t\talignItems: 'center'\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t},\r\n\t\t\tleft(type) {\r\n\t\t\t\tthis.popupClass = 'left'\r\n\t\t\t\tthis.ani = ['slide-left']\r\n\t\t\t\tthis.transitionStyle = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tzIndex: this.zIndex,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tbackgroundColor: this.bg,\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\tflexDirection: 'column'\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t},\r\n\t\t\tright(type) {\r\n\t\t\t\tthis.popupClass = 'right'\r\n\t\t\t\tthis.ani = ['slide-right']\r\n\t\t\t\tthis.transitionStyle = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tzIndex: this.zIndex,\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tbackgroundColor: this.bg,\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\tflexDirection: 'column'\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.uv-popup {\r\n\t\tposition: fixed;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tz-index: 99;\r\n\r\n\t\t/* #endif */\r\n\t\t&.top,\r\n\t\t&.left,\r\n\t\t&.right {\r\n\t\t\t/* #ifdef H5 */\r\n\t\t\ttop: var(--window-top);\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifndef H5 */\r\n\t\t\ttop: 0;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\r\n\t\t.uv-popup__content {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tdisplay: block;\r\n\t\t\toverflow: hidden;\r\n\t\t\t/* #endif */\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&.left,\r\n\t\t\t&.right {\r\n\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\tpadding-top: var(--window-top);\r\n\t\t\t\t/* #endif */\r\n\t\t\t\t/* #ifndef H5 */\r\n\t\t\t\tpadding-top: 0;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\t\t\t&__close {\r\n\t\t\t\tposition: absolute;\r\n\r\n\t\t\t\t&--hover {\r\n\t\t\t\t\topacity: 0.4;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&__close--top-left {\r\n\t\t\t\ttop: 15px;\r\n\t\t\t\tleft: 15px;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&__close--top-right {\r\n\t\t\t\ttop: 15px;\r\n\t\t\t\tright: 15px;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&__close--bottom-left {\r\n\t\t\t\tbottom: 15px;\r\n\t\t\t\tleft: 15px;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&__close--bottom-right {\r\n\t\t\t\tright: 15px;\r\n\t\t\t\tbottom: 15px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.fixforpc-z-index {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tz-index: 999;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.fixforpc-top {\r\n\t\ttop: 0;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-popup.vue?vue&type=style&index=0&id=4cc3c370&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-popup.vue?vue&type=style&index=0&id=4cc3c370&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759141819037\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}