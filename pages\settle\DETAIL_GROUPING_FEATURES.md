# 工单详情分组显示功能

## 功能概述
工单详情弹窗现在按照业务流程分为三个主要信息组：申请信息、审核信息、付款信息，让用户能够清晰地了解结算流程的每个阶段。

## 信息分组结构

### 1. 申请信息 (始终显示)
显示工单的基本信息和申请结算的相关数据：
- **工单名称**: 显示工单的标题
- **申请结算金额**: 显示申请的结算金额（红色高亮）
- **申请时间**: 显示提交结算申请的时间 (createTime)
- **备注**: 显示申请时填写的备注信息 (applyRemark)

### 2. 审核信息 (有审核状态时显示)
当 `payStatus` 不为 null 时显示，包含审核相关信息：
- **审核状态**: 显示当前审核状态，带颜色标识
  - 待审核 (橙色)
  - 审核通过 (绿色)
  - 已付款 (深绿色)
  - 审核失败 (红色)
- **审核备注**: 显示审核人员的备注信息
  - 审核失败时使用特殊样式高亮显示
- **审核时间**: 显示审核完成的时间 (auditTime)

### 3. 付款信息 (已付款时显示)
当 `payStatus = 2` (已付款) 时显示：
- **付款时间**: 显示实际付款的时间 (payTime)
- **付款金额**: 显示实际付款金额 (payAmount，绿色显示)
- **付款照片**: 显示付款凭证照片
  - 支持多张图片展示
  - 点击可预览大图
  - 网格布局显示

## 视觉设计特点

### 分组样式
- 每个信息组使用独立的卡片容器
- 浅灰色背景 (#f8f9fa)
- 左侧蓝色边框标识
- 圆角设计增强视觉效果

### 标题样式
- 蓝色主题色 (#2d8cf0)
- 加粗字体突出重要性
- 底部分割线区分标题和内容

### 状态颜色系统
- **待结算**: 橙色 (#ff9900)
- **待审核**: 蓝色 (#2d8cf0)
- **审核通过**: 绿色 (#19be6b)
- **已付款**: 深绿色 (#67c23a)
- **审核失败**: 红色 (#f56c6c)

### 特殊样式
- **金额显示**: 红色字体，加粗显示
- **审核失败原因**: 红色背景，左边框强调
- **付款照片**: 网格布局，悬停放大效果

## 交互功能

### 图片预览
- 点击付款照片可以预览大图
- 支持多张图片滑动查看
- 使用 uni.previewImage API

### 条件显示
- 根据工单状态智能显示相关信息组
- 避免显示无关或空白信息
- 提升用户体验

## 数据字段映射

### 申请信息字段
```javascript
{
  objectName: "工单名称",
  totalPrice: "申请结算金额", 
  createTime: "申请时间",
  applyRemark: "备注"
}
```

### 审核信息字段
```javascript
{
  payStatus: "审核状态值",
  auditRemark: "审核备注",
  auditTime: "审核时间"
}
```

### 付款信息字段
```javascript
{
  payTime: "付款时间",
  payAmount: "付款金额",
  payPhotos: ["付款照片数组"]
}
```

## 模拟数据示例

每个状态都提供了完整的模拟数据：
- **待结算**: 只有基本申请信息
- **待审核**: 包含申请信息，无审核结果
- **审核通过**: 包含申请和审核信息
- **已付款**: 包含完整的三组信息
- **审核失败**: 包含申请和审核失败信息

## 用户体验优化

1. **信息层次清晰**: 按流程分组，便于理解
2. **视觉引导**: 颜色和样式引导用户关注重点
3. **交互友好**: 图片预览等增强功能
4. **响应式设计**: 适配不同屏幕尺寸
5. **状态感知**: 根据实际状态显示相关信息
