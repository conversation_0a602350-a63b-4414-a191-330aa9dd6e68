(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/settle/settle"],{2720:function(t,e,n){"use strict";(function(t,e){var o=n("47a9");n("ea4a");o(n("3240"));var i=o(n("8b56"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},3200:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return o}));var o={uSearch:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-search/u-search")]).then(n.bind(null,"716b"))},uPopup:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-popup/u-popup")]).then(n.bind(null,"1a2c"))},uInput:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-input/u-input")]).then(n.bind(null,"4a78"))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-button/u-button")]).then(n.bind(null,"f8f8"))},uDatetimePicker:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker")]).then(n.bind(null,"d33a"))},uniLoadMore:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(n.bind(null,"4357"))},uForm:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-form/u-form")]).then(n.bind(null,"a7f1"))},uFormItem:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-form-item/u-form-item")]).then(n.bind(null,"1da9"))},uTextarea:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-textarea/u-textarea")]).then(n.bind(null,"6704"))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.list.length),o=t.__map(t.list,(function(e,n){var o=t.__get_orig(e),i=t.getSettleStatus(e),a=t.getSettleStatusText(e);return{$orig:o,m0:i,m1:a}})),i=t.list.length,a=t.currentItem&&null!==t.currentItem.payStatus&&void 0!==t.currentItem.payStatus?t.getSettleStatus(t.currentItem):null,u=t.currentItem&&null!==t.currentItem.payStatus&&void 0!==t.currentItem.payStatus?t.getSettleStatusText(t.currentItem):null,r=t.currentItem&&null!==t.currentItem.payStatus&&void 0!==t.currentItem.payStatus&&t.currentItem.auditRemark?t.getSettleStatus(t.currentItem):null,s=t.currentItem&&2===t.currentItem.payStatus?t.currentItem.payPhotos&&t.currentItem.payPhotos.length>0:null;t._isMounted||(t.e0=function(e){t.showDetailPopup=!1},t.e1=function(e){t.showFilterPopup=!1},t.e2=function(e){t.showStartDatePicker=!0},t.e3=function(e){t.showEndDatePicker=!0},t.e4=function(e){t.showStartDatePicker=!1},t.e5=function(e){t.showStartDatePicker=!1},t.e6=function(e){t.showEndDatePicker=!1},t.e7=function(e){t.showEndDatePicker=!1},t.e8=function(e){t.showDetailPopup=!1},t.e9=function(e){t.showSettlePopup=!1}),t.$mp.data=Object.assign({},{$root:{g0:n,l0:o,g1:i,m2:a,m3:u,m4:r,g2:s}})},a=[]},"8b56":function(t,e,n){"use strict";n.r(e);var o=n("3200"),i=n("9bef");for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);n("e051");var u=n("828b"),r=Object(u["a"])(i["default"],o["b"],o["c"],!1,null,"0371fa84",null,!1,o["a"],void 0);e["default"]=r.exports},"9bef":function(t,e,n){"use strict";n.r(e);var o=n("fdc5"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=i.a},"9d24":function(t,e,n){},e051:function(t,e,n){"use strict";var o=n("9d24"),i=n.n(o);i.a},fdc5:function(t,e,n){"use strict";(function(t){var o=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=o(n("af34")),a=n("389b"),u={name:"Settle",data:function(){return{dateFormat:a.dateFormat,keyWords:"",currentTab:0,list:[],page:{size:4,current:1,total:0},summary:{totalAmount:0,waiSettlementPrice:0,settlementPrice:0,paymentPrice:0},tabList:[{label:"待审核",value:"0"},{label:"审核通过",value:"1"},{label:"已付款",value:"2"},{label:"审核失败",value:"3"}],statusMap:{2:"已完成",3:"待接单",1:"进行中"},showFilterPopup:!1,showStartDatePicker:!1,showEndDatePicker:!1,filterForm:{minAmount:"",maxAmount:"",startDate:"",endDate:""},showDetailPopup:!1,showSettlePopup:!1,currentItem:null,settleForm:{id:"",totalPrice:"",applyContent:""}}},onReady:function(){this.getList(),this.getSettleSummary()},onShow:function(){},onPullDownRefresh:function(){this.refreshData()},onReachBottom:function(){this.scrolltolower()},methods:{getSettleSummary:function(){var t=this;this.$u.api.getSettlementStatistics().then((function(e){t.summary=e.data||{totalAmount:0,waiSettlementPrice:0,settlementPrice:0,paymentPrice:0}})).catch((function(){}))},refreshData:function(){this.page.current=1,this.list=[],this.getList(),this.getSettleSummary(),t.stopPullDownRefresh()},getList:function(){var t=this,e={size:this.page.size,current:this.page.current,objectName:this.keyWords},n=this.tabList[this.currentTab].value;e.payStatus=n,this.filterForm.minAmount&&(e.minAmount=this.filterForm.minAmount),this.filterForm.maxAmount&&(e.maxAmount=this.filterForm.maxAmount),this.filterForm.startDate&&(e.startDate=this.filterForm.startDate),this.filterForm.endDate&&(e.endDate=this.filterForm.endDate),this.$u.api.getSettlementApply(e).then((function(e){t.list=[].concat((0,i.default)(t.list),(0,i.default)(e.data.records)),t.page.total=e.data.total})).catch((function(){t.loadMockData()}))},loadMockData:function(){var t=this.tabList[this.currentTab].value,e=[];"pending"===t?e=[{id:1,objectName:"空调维修工单",serverTypeName:"维修服务",finalCustomer:"张三公司",contact:"张三",contactPhone:"13800138001",distributionAddress:"北京市朝阳区xxx街道",completeTime:"2024-01-15 14:30:00",totalPrice:1200,objectStatus:2,payStatus:null}]:"auditing"===t?e=[{id:2,objectName:"电梯保养工单",serverTypeName:"保养服务",finalCustomer:"李四大厦",contact:"李四",contactPhone:"13800138002",distributionAddress:"上海市浦东新区xxx路",completeTime:"2024-01-14 16:45:00",totalPrice:800,createTime:"2024-01-15 09:30:00",applyRemark:"电梯保养完成，设备运行正常",objectStatus:2,payStatus:0}]:"approved"===t?e=[{id:3,objectName:"网络设备维护",serverTypeName:"维护服务",finalCustomer:"王五科技",contact:"王五",contactPhone:"13800138003",distributionAddress:"深圳市南山区xxx大厦",completeTime:"2024-01-13 10:20:00",totalPrice:1500,createTime:"2024-01-14 11:15:00",applyRemark:"网络设备维护完成，系统运行稳定",auditTime:"2024-01-14 15:30:00",auditRemark:"审核通过，费用合理",objectStatus:2,payStatus:1}]:"settled"===t?e=[{id:4,objectName:"消防设备检修",serverTypeName:"检修服务",finalCustomer:"赵六集团",contact:"赵六",contactPhone:"13800138004",distributionAddress:"广州市天河区xxx中心",completeTime:"2024-01-12 15:45:00",totalPrice:2e3,createTime:"2024-01-13 08:20:00",applyRemark:"消防设备检修完成，所有设备正常",auditTime:"2024-01-13 14:30:00",auditRemark:"审核通过，检修质量良好",payTime:"2024-01-14 10:15:00",payAmount:2e3,payPhotos:["https://example.com/pay1.jpg","https://example.com/pay2.jpg"],objectStatus:2,payStatus:2}]:"rejected"===t&&(e=[{id:5,objectName:"监控系统维修",serverTypeName:"维修服务",finalCustomer:"孙七物业",contact:"孙七",contactPhone:"13800138005",distributionAddress:"杭州市西湖区xxx小区",completeTime:"2024-01-11 13:15:00",totalPrice:900,createTime:"2024-01-12 09:45:00",applyRemark:"监控系统维修完成，画面清晰",auditTime:"2024-01-12 16:20:00",auditRemark:"审核失败：费用明细不清晰，缺少材料费用清单，请重新提交详细的费用说明",objectStatus:2,payStatus:3}]),this.list=[].concat((0,i.default)(this.list),(0,i.default)(e)),this.page.total=e.length},scrolltolower:function(){this.list.length!=this.page.total&&(this.page.current++,this.getList())},handleTabChange:function(t){this.currentTab=t,this.page.current=1,this.list=[],this.getList()},handleSearch:function(){this.list=[],this.page.current=1,this.getList()},resetFilter:function(){this.filterForm={minAmount:"",maxAmount:"",startDate:"",endDate:""}},applyFilter:function(){this.showFilterPopup=!1,this.page.current=1,this.list=[],this.getList()},getSettleStatus:function(t){return null===t.payStatus||void 0===t.payStatus?"pending":t.payStatus},getSettleStatusText:function(t){return{0:"待审核",1:"审核通过",2:"已付款",3:"审核失败"}[t.payStatus]},showOrderDetail:function(t){this.currentItem=t,this.showDetailPopup=!0},handleApplySettle:function(t){this.currentItem=t,this.settleForm={id:t.id,totalPrice:t.totalPrice,applyContent:"",payStatus:0},this.showSettlePopup=!0},submitSettle:function(){var t=this;this.settleForm.totalPrice?this.$u.api.editSettlement(this.settleForm).then((function(){t.$u.toast("申请成功"),t.showSettlePopup=!1,t.settleForm={id:"",totalPrice:"",applyContent:""},t.refreshData()})).catch((function(e){t.$u.toast(e.message||"申请失败")})):this.$u.toast("请输入结算金额")},makePhoneCall:function(e){t.makePhoneCall({phoneNumber:e,success:function(){console.log("拨打电话成功！")},fail:function(t){console.log("拨打电话失败！",t)}})},previewImage:function(e,n){t.previewImage({current:e,urls:n,success:function(){console.log("预览图片成功")},fail:function(t){console.log("预览图片失败",t)}})}}};e.default=u}).call(this,n("df3c")["default"])}},[["2720","common/runtime","common/vendor"]]]);