{"version": 3, "sources": ["webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-preview-video/uv-preview-video.vue?8573", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-preview-video/uv-preview-video.vue?7f58", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-preview-video/uv-preview-video.vue?1e1e", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-preview-video/uv-preview-video.vue?43b1", "uni-app:///uni_modules/uv-upload/components/uv-preview-video/uv-preview-video.vue", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-preview-video/uv-preview-video.vue?4111", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-upload/components/uv-preview-video/uv-preview-video.vue?89ce"], "names": ["props", "src", "type", "default", "autoplay", "data", "videoSrc", "show", "computed", "getSec", "methods", "open", "close", "change"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA2oB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eCQ/pB;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAsuC,CAAgB,ioCAAG,EAAC,C;;;;;;;;;;;ACA1vC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uv-upload/components/uv-preview-video/uv-preview-video.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uv-preview-video.vue?vue&type=template&id=67ad7bc6&scoped=true&\"\nvar renderjs\nimport script from \"./uv-preview-video.vue?vue&type=script&lang=js&\"\nexport * from \"./uv-preview-video.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uv-preview-video.vue?vue&type=style&index=0&id=67ad7bc6&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"67ad7bc6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uv-upload/components/uv-preview-video/uv-preview-video.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-preview-video.vue?vue&type=template&id=67ad7bc6&scoped=true&\"", "var components\ntry {\n  components = {\n    uvPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-popup/components/uv-popup/uv-popup\" */ \"@/uni_modules/uv-popup/components/uv-popup/uv-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-preview-video.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-preview-video.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<uv-popup ref=\"popup\" @change=\"change\">\r\n\t\t<view class=\"video-view\" v-if=\"show\">\r\n\t\t\t<video class=\"video\" :src=\"getSec\" :autoplay=\"autoplay\"></video>\r\n\t\t</view>\r\n\t</uv-popup>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tsrc: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tautoplay: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tvideoSrc: '',\r\n\t\t\t\tshow: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tgetSec() {\r\n\t\t\t\treturn this.src || this.videoSrc;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\topen(url) {\r\n\t\t\t\tthis.videoSrc = url;\r\n\t\t\t\tthis.$refs.popup.open();\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.$refs.popup.close();\r\n\t\t\t},\r\n\t\t\tchange(e) {\r\n\t\t\t\tthis.show = e.show;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\t.video-view {\r\n\t\twidth: 750rpx;\r\n\t\t.video {\r\n\t\t\twidth: 750rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-preview-video.vue?vue&type=style&index=0&id=67ad7bc6&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-preview-video.vue?vue&type=style&index=0&id=67ad7bc6&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759141818726\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}