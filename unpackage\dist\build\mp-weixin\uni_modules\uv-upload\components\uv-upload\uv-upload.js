(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uv-upload/components/uv-upload/uv-upload"],{"2c14":function(e,t,i){"use strict";i.r(t);var n=i("2f9b"),a=i.n(n);for(var u in n)["default"].indexOf(u)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(u);t["default"]=a.a},"2dd7":function(e,t,i){"use strict";var n=i("d151"),a=i.n(n);a.a},"2f9b":function(e,t,i){"use strict";(function(e){var n=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i("e1d3"),u=n(i("d32d")),o=n(i("1b07")),s=i("f0eb"),l=n(i("d160")),r=n(i("4160")),c={name:"uv-upload",mixins:[u.default,o.default,l.default,r.default],data:function(){return{lists:[],isInCount:!0}},watch:{fileList:{deep:!0,immediate:!0,handler:function(){this.formatFileList()}},deletable:function(e){var t=this;e||this.lists.map((function(e){e.deletable=t.deletable}))}},methods:{formatFileList:function(){var e=this,t=this.fileList,i=void 0===t?[]:t,n=this.maxCount,u=i.map((function(t){return Object.assign(Object.assign({},t),{isImage:"image"===e.accept||(0,a.image)(t.url||t.thumb),isVideo:"video"===e.accept||(0,a.video)(t.url||t.thumb),deletable:"boolean"===typeof t.deletable?t.deletable:e.deletable})}));this.lists=u,this.isInCount=u.length<n},chooseFile:function(){var e=this;this.timer&&clearTimeout(this.timer),this.timer=setTimeout((function(){var t=e.maxCount,i=e.multiple,n=e.lists,u=e.disabled;if(!u){var o;try{o=(0,a.array)(e.capture)?e.capture:e.capture.split(",")}catch(l){o=[]}(0,s.chooseFile)(Object.assign({accept:e.accept,multiple:e.multiple,capture:o,compressed:e.compressed,maxDuration:e.maxDuration,sizeType:e.sizeType,camera:e.camera},{maxCount:t-n.length})).then((function(t){e.onBeforeRead(i?t:t[0])})).catch((function(t){e.$emit("error",t)}))}}),100)},onBeforeRead:function(e){var t=this,i=this.beforeRead,n=this.useBeforeRead,u=!0;(0,a.func)(i)&&(u=i(e,this.getDetail())),n&&(u=new Promise((function(i,n){t.$emit("beforeRead",Object.assign(Object.assign({file:e},t.getDetail()),{callback:function(e){e?i():n()}}))}))),u&&((0,a.promise)(u)?u.then((function(i){return t.onAfterRead(i||e)})):this.onAfterRead(e))},getDetail:function(e){return{name:this.name,index:null==e?this.fileList.length:e}},onAfterRead:function(e){var t=this.maxSize,i=this.afterRead,n=Array.isArray(e)?e.some((function(e){return e.size>t})):e.size>t;n?this.$emit("oversize",Object.assign({file:e},this.getDetail())):("function"===typeof i&&i(e,this.getDetail()),this.$emit("afterRead",Object.assign({file:e},this.getDetail())))},deleteItem:function(e){this.$emit("delete",Object.assign(Object.assign({},this.getDetail(e)),{file:this.fileList[e]}))},onPreviewImage:function(t,i){var n=this,u=this.$uv.deepClone(this.lists);u.map((function(e,t){t==i&&(e.current=!0)}));var o=u.filter((function(e){return e.isImage})),s=o.findIndex((function(e){return e.current}));this.onClickPreview(t,i),t.isImage&&this.previewFullImage&&e.previewImage({urls:this.lists.filter((function(e){return"image"===n.accept||(0,a.image)(e.url||e.thumb)})).map((function(e){return e.url||e.thumb})),current:s,fail:function(){this.$uv.toast("预览图片失败")}})},onPreviewVideo:function(e,t){this.onClickPreview(e,t),this.previewFullVideo&&e.isVideo&&this.$refs.previewVideo.open(e.url)},onClickPreview:function(e,t){this.$emit("clickPreview",Object.assign(Object.assign({},e),this.getDetail(t)))}}};t.default=c}).call(this,i("df3c")["default"])},a87e:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return u})),i.d(t,"a",(function(){return n}));var n={uvIcon:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uv-icon/components/uv-icon/uv-icon")]).then(i.bind(null,"b0b0"))},uvLoadingIcon:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uv-loading-icon/components/uv-loading-icon/uv-loading-icon")]).then(i.bind(null,"e0c5"))},uvPreviewVideo:function(){return i.e("uni_modules/uv-upload/components/uv-preview-video/uv-preview-video").then(i.bind(null,"65d2"))}},a=function(){var e=this,t=e.$createElement,i=(e._self._c,e.__get_style([e.$uv.addStyle(e.customStyle)])),n=e.previewImage?e.__map(e.lists,(function(t,i){var n=e.__get_orig(t),a=t.isImage||t.type&&"image"===t.type?e.$uv.addUnit(e.width):null,u=t.isImage||t.type&&"image"===t.type?e.$uv.addUnit(e.height):null,o=t.isImage||t.type&&"image"===t.type?null:e.$uv.addUnit(e.width),s=t.isImage||t.type&&"image"===t.type?null:e.$uv.addUnit(e.height);return{$orig:n,g0:a,g1:u,g2:o,g3:s}})):null,a=e.isInCount?e.$uv.addUnit(e.width):null,u=e.isInCount?e.$uv.addUnit(e.height):null;e.$mp.data=Object.assign({},{$root:{s0:i,l0:n,g4:a,g5:u}})},u=[]},d151:function(e,t,i){},ddd1:function(e,t,i){"use strict";i.r(t);var n=i("a87e"),a=i("2c14");for(var u in a)["default"].indexOf(u)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(u);i("2dd7");var o=i("828b"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"e6e7992a",null,!1,n["a"],void 0);t["default"]=s.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uv-upload/components/uv-upload/uv-upload-create-component',
    {
        'uni_modules/uv-upload/components/uv-upload/uv-upload-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("ddd1"))
        })
    },
    [['uni_modules/uv-upload/components/uv-upload/uv-upload-create-component']]
]);
