{"version": 3, "sources": ["webpack:///D:/project/vt-unih5-order/uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar.vue?7afe", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar.vue?f987", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar.vue?c67e", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar.vue?5c15", "uni-app:///uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar.vue", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar.vue?089e", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar.vue?7ee9"], "names": ["name", "mixins", "data", "computed", "style"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAwoB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACU5pB;AACA;AACA;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,eAQA;EACAA;EACAC;EACAC;IACA,QACA;EACA;EACAC;IACAC;MACA;MACA;MACAA;MACA;QACA;UAAA;UACAA;QACA;UACAA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAmuC,CAAgB,8nCAAG,EAAC,C;;;;;;;;;;;ACAvvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uv-status-bar.vue?vue&type=template&id=20b7b190&scoped=true&\"\nvar renderjs\nimport script from \"./uv-status-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./uv-status-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uv-status-bar.vue?vue&type=style&index=0&id=20b7b190&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"20b7b190\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-status-bar.vue?vue&type=template&id=20b7b190&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.style])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-status-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-status-bar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t    :style=\"[style]\"\r\n\t    class=\"uv-status-bar\"\r\n\t>\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport mpMixin from '@/uni_modules/uv-ui-tools/libs/mixin/mpMixin.js'\r\n\timport mixin from '@/uni_modules/uv-ui-tools/libs/mixin/mixin.js'\r\n\timport props from './props.js';\r\n\t/**\r\n\t * StatbusBar 状态栏占位\r\n\t * @description 本组件主要用于状态填充，比如在自定导航栏的时候，它会自动适配一个恰当的状态栏高度。\r\n\t * @tutorial https://www.uvui.cn/components/statusBar.html\r\n\t * @property {String}\t\t\tbgColor\t\t\t背景色 (默认 'transparent' )\r\n\t * @property {String | Object}\tcustomStyle\t\t自定义样式 \r\n\t * @example <uv-status-bar></uv-status-bar>\r\n\t */\r\n\texport default {\r\n\t\tname: 'uv-status-bar',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tstyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\t// 状态栏高度，由于某些安卓和微信开发工具无法识别css的顶部状态栏变量，所以使用js获取的方式\r\n\t\t\t\tstyle.height = this.$uv.addUnit(this.$uv.sys().statusBarHeight, 'px')\r\n\t\t\t\tif(this.bgColor){\r\n\t\t\t\t\tif (this.bgColor.indexOf(\"gradient\") > -1) {// 渐变色\r\n\t\t\t\t\t\tstyle.backgroundImage = this.bgColor;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tstyle.background = this.bgColor;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn this.$uv.deepMerge(style, this.$uv.addStyle(this.customStyle))\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.uv-status-bar {\r\n\t\t// nvue会默认100%，如果nvue下，显式写100%的话，会导致宽度不为100%而异常\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twidth: 100%;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-status-bar.vue?vue&type=style&index=0&id=20b7b190&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-status-bar.vue?vue&type=style&index=0&id=20b7b190&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759141819053\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}