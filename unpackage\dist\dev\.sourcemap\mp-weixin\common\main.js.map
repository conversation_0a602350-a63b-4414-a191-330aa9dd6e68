{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/vt-unih5-order/App.vue?17bb", "uni-app:///App.vue", "webpack:///D:/project/vt-unih5-order/App.vue?d39d", "webpack:///D:/project/vt-unih5-order/App.vue?0eb6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "use", "uView", "App", "mpType", "app", "globalFunc", "httpInstall", "$mount", "updateManager", "uni", "getUpdateManager", "onCheckForUpdate", "res", "console", "log", "hasUpdate", "onUpdateReady", "showModal", "title", "content", "success", "confirm", "applyUpdate", "onUpdateFailed", "onLaunch", "onShow", "onHide"], "mappings": ";;;;;;;;;;;;;;AAAA;AAGA;AACA;AAGA;AAUA;AAEA;AAAwC;AAAA;AAlBxC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1D;AACAC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCF,YAAG,CAACG,GAAG,CAACC,gBAAK,CAAC;AACdC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB;AACA;AACA;AACA,IAAMC,GAAG,GAAG,IAAIP,YAAG,mBACfK,YAAG,EACL;AACF;;AAIAL,YAAG,CAACG,GAAG,CAACK,aAAU,EAAED,GAAG,CAAC;AACxBP,YAAG,CAACG,GAAG,CAACM,gBAAW,EAAEF,GAAG,CAAC;AACzB,UAAAA,GAAG,EAACG,MAAM,EAAE;AAEZ,IAAMC,aAAa,GAAGC,GAAG,CAACC,gBAAgB,EAAE;AAE5CF,aAAa,CAACG,gBAAgB,CAAC,UAASC,GAAG,EAAE;EAC5C;EACAC,OAAO,CAACC,GAAG,CAACF,GAAG,CAACG,SAAS,EAAE,SAAS,CAAC;AACtC,CAAC,CAAC;AAEFP,aAAa,CAACQ,aAAa,CAAC,UAASJ,GAAG,EAAE;EACzCH,GAAG,CAACQ,SAAS,CAAC;IACbC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,kBAAkB;IAC3BC,OAAO,mBAACR,GAAG,EAAE;MACZ,IAAIA,GAAG,CAACS,OAAO,EAAE;QAChB;QACAb,aAAa,CAACc,WAAW,EAAE;MAC5B;IACD;EACD,CAAC,CAAC;AAEH,CAAC,CAAC;AAEFd,aAAa,CAACe,cAAc,CAAC,UAASX,GAAG,EAAE;EAC1C;AAAA,CACA,CAAC,C;;;;;;;;;;;;;AC/CF;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AAC6J;AAC7J,gBAAgB,6KAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAkkB,CAAgB,6mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCCtlB;EACAY;IACAX;IACAA;EACA;EACAY;IACAZ;EACA;EACAa;IACAb;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACZA;AAAA;AAAA;AAAA;AAAqlC,CAAgB,4lCAAG,EAAC,C;;;;;;;;;;;ACAzmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\r\nimport Vue from 'vue'\r\nimport App from './App'\r\n// import store from '@/store';\r\nVue.config.productionTip = false\r\nimport uView from '@/uni_modules/uview-ui'\r\nVue.use(uView)\r\nApp.mpType = 'app'\r\n// // 引入vuex\r\n// const vuexStore = require(\"@/store/$u.mixin.js\");\r\n// Vue.mixin(vuexStore);\r\nconst app = new Vue({\r\n\t...App\r\n})\r\n// 接口集中管理\r\nimport httpInstall from '@/http/install.js'\r\n// 公共函数\r\nimport globalFunc from '@/utils/func.js'\r\nVue.use(globalFunc, app);\r\nVue.use(httpInstall, app)\r\napp.$mount()\r\n\r\nconst updateManager = uni.getUpdateManager();\r\n\r\nupdateManager.onCheckForUpdate(function(res) {\r\n\t// 请求完新版本信息的回调\r\n\tconsole.log(res.hasUpdate, '版本是否有更新');\r\n});\r\n\r\nupdateManager.onUpdateReady(function(res) {\r\n\tuni.showModal({\r\n\t\ttitle: '更新提示',\r\n\t\tcontent: '新版本已经准备好，是否重启应用？',\r\n\t\tsuccess(res) {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\t// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启\r\n\t\t\t\tupdateManager.applyUpdate();\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n\r\n});\r\n\r\nupdateManager.onUpdateFailed(function(res) {\r\n\t// 新的版本下载失败\r\n});", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\n\t\t\tconsole.warn('当前组件仅支持 uni_modules 目录结构 ，请升级 HBuilderX 到 3.1.0 版本以上！')\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import \"@/uni_modules/uview-ui/index.scss\";\r\n\t/*每个页面公共css */\n\t@import '@/uni_modules/uni-scss/index.scss';\n\t/* #ifndef APP-NVUE */\n\t@import '@/static/customicons.css';\n\t// 设置整个项目的背景色\n\tpage {\n\t\tbackground-color: #f5f5f5;\n\t}\n\n\t/* #endif */\n\t.example-info {\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t\tpadding: 10px;\n\t}\r\n</style>\n", "import mod from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759141819069\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}