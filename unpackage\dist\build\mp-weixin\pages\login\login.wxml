<view class="login-container data-v-54e106d6"><view class="login-card data-v-54e106d6"><view class="header-section data-v-54e106d6"><view class="title data-v-54e106d6">登录</view><view class="subtitle data-v-54e106d6">请输入手机号或使用微信快捷登录</view></view><view class="form-section data-v-54e106d6"><u-form vue-id="35a7246c-1" model="{{form}}" rules="{{rules}}" data-ref="uForm" class="data-v-54e106d6 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('35a7246c-2')+','+('35a7246c-1')}}" prop="phone" borderBottom="{{true}}" class="data-v-54e106d6" bind:__l="__l" vue-slots="{{['default']}}"><u-input vue-id="{{('35a7246c-3')+','+('35a7246c-2')}}" placeholder="请输入手机号" type="number" maxlength="11" prefixIcon="phone" clearable="{{true}}" customStyle="{{inputStyle}}" value="{{form.phone}}" data-event-opts="{{[['^focus',[['onInputFocus']]],['^blur',[['onInputBlur']]],['^input',[['__set_model',['$0','phone','$event',[]],['form']]]]]}}" bind:focus="__e" bind:blur="__e" bind:input="__e" class="data-v-54e106d6" bind:__l="__l" vue-slots="{{['suffix']}}"><view slot="suffix"><block wx:if="{{form.phone&&!isValidPhone}}"><u-button vue-id="{{('35a7246c-4')+','+('35a7246c-3')}}" text="格式错误" type="error" size="mini" disabled="{{true}}" class="data-v-54e106d6" bind:__l="__l"></u-button></block><block wx:else><block wx:if="{{form.phone&&isValidPhone}}"><u-button vue-id="{{('35a7246c-5')+','+('35a7246c-3')}}" text="格式正确" type="success" size="mini" disabled="{{true}}" class="data-v-54e106d6" bind:__l="__l"></u-button></block></block></view></u-input></u-form-item></u-form><view class="button-section data-v-54e106d6"><u-button vue-id="35a7246c-6" text="{{loginButtonText}}" type="primary" size="large" loading="{{isLoading}}" disabled="{{!canLogin}}" customStyle="{{primaryButtonStyle}}" data-event-opts="{{[['^click',[['handlePhoneLogin']]]]}}" bind:click="__e" class="data-v-54e106d6" bind:__l="__l"></u-button></view><view class="divider-section data-v-54e106d6"><view class="divider-line data-v-54e106d6"></view><view class="divider-text data-v-54e106d6">或</view><view class="divider-line data-v-54e106d6"></view></view><view class="wechat-section data-v-54e106d6"><block wx:if="{{!form.phone}}"><view class="quick-phone-section data-v-54e106d6"><u-button vue-id="35a7246c-7" text="微信授权获取" type="info" size="small" open-type="getPhoneNumber" plain="{{true}}" data-event-opts="{{[['^getphonenumber',[['getPhoneNumber']]]]}}" bind:getphonenumber="__e" class="data-v-54e106d6" bind:__l="__l"></u-button></view></block></view></view></view><view class="footer-section data-v-54e106d6"><view class="privacy-text data-v-54e106d6">登录即表示同意<text class="link-text data-v-54e106d6">《用户协议》</text>和<text class="link-text data-v-54e106d6">《隐私政策》</text></view></view></view>