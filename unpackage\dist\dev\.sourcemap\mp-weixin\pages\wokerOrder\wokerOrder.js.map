{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?43bd", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?f03d", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?49b7", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?f4d2", "uni-app:///pages/wokerOrder/wokerOrder.vue", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?3716", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrder.vue?2295"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "key<PERSON>ords", "currentStatus", "dateFormat", "list", "showFinishPopup", "showFinishTimePicker", "modalShow", "finishForm", "finishTime", "remark", "fileList", "page", "size", "current", "total", "subList", "label", "value", "payStatus", "show", "showSettlePopup", "settleForm", "summary", "totalNum", "wait<PERSON>um", "do<PERSON>um", "completeNum", "statusMap", "payStatusMap", "showFilterPopup", "showStartDatePicker", "showEndDatePicker", "filterForm", "startDate", "endDate", "onReady", "onShow", "onPullDownRefresh", "onReachBottom", "methods", "getTaskStatistics", "refreshData", "uni", "getList", "objectStatus", "objectName", "params", "getWorkerOrder", "then", "scrolltolower", "console", "sectionChange", "handleSearch", "handleStart", "confirm", "handleFinish", "afterRead", "file", "item", "status", "message", "index", "uploadFile", "filePath", "http", "res", "handleDelete", "handleClickPreview", "submit<PERSON><PERSON>sh", "id", "completeRemark", "completeFiles", "finishWorkerOrder", "catch", "toDetail", "url", "validatePhone", "toUserInfo", "handleSettled", "objectId", "totalPrice", "submitSettle", "resetFilter", "applyFilter", "makePhoneCall", "phoneNumber", "success", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtKA;AAAA;AAAA;AAAA;AAAumB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACqV3nB;AACA;AAAA;AAAA;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC,UACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC,YACA;QACAD;QACAD;MACA,GACA;QACAC;QACAD;MACA,GACA;QACAC;QACAD;MACA,GACA;QACAC;QACAD;MACA,EACA;MACAG;MACAC;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACA;QACA;QACA;MACA;MACAC;QACA;QACA;QACA;QACA;MACA;MACA;MACAC;MACAC;MACAC;MACAC;QACAd;QACAe;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;MACA;QACA;QACA;UACAjB;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACA;IACAe;MACA;MACA;MACA;MACA;MACAC;IACA;IACAC;MAAA;MACA;QACA/B;QACAC;QACA+B;QACAC;MACA;;MAEA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MAEA,YACAC,uBACAC;QACA;QACA;MACA;IACA;IACAC;MACAC;MAEA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACAN;MACA;MACA;MACAO;QACA,gEACAC;UACAC;UACAC;UACA;UACAC;QAAA,GACA;MACA;MACAJ;QACA;MACA;IACA;IACAK;MAAA;MACA;QACA;UACAC;UACAjE;QACA;QACAkE;UACA;YAAA;UAAA,YACA;UACA;YAAA;UAAA,aACA;UACA;YAAA;UAAA,SACAC;UACA;YAAA;UAAA,QACAA;QACA;MACA;IACA;IACAC;MAAA;QAAAL;QAAA/D;MACAoD;MACA;IACA;IACAiB;MACAjB;IACA;IAEAkB;MAAA;MACA;QACA;QACA;MACA;MACA;QACAC;QACA7D,4BACA,8CACA,sBACA;QACA8D;QACAC,eACA,4BACA;UAAA;QAAA;MACA;MACA,YACAC,4BACAxB;QACA;QACA;QACA;UACAxC;UACAC;UACAC;QACA;QACA;MACA,GACA+D;QACA;MACA;IACA;IACAC;MACAhC;QACAiC;MACA;IACA;IACAC;MAAA;MACA;QACA;QAEA;UACA;QACA;UACA1B;UACA;QACA;MACA;IACA;IACA2B;MACAnC;QACAiC;MACA;IACA;IACAG;MACA;MACA;QACAC;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;UACAF;UACAC;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAE;MACA;QACAhE;QACAe;QACAC;MACA;IACA;IACAiD;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA1C;QACA2C;QACAC;UACApC;QACA;QACAqC;UACArC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7pBA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/wokerOrder/wokerOrder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/wokerOrder/wokerOrder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./wokerOrder.vue?vue&type=template&id=0ec3f686&scoped=true&\"\nvar renderjs\nimport script from \"./wokerOrder.vue?vue&type=script&lang=js&\"\nexport * from \"./wokerOrder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wokerOrder.vue?vue&type=style&index=0&id=0ec3f686&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0ec3f686\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/wokerOrder/wokerOrder.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrder.vue?vue&type=template&id=0ec3f686&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-search/u-search\" */ \"@/uni_modules/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"@/uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-modal/u-modal\" */ \"@/uni_modules/uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uTextarea: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-textarea/u-textarea\" */ \"@/uni_modules/uview-ui/components/u-textarea/u-textarea.vue\"\n      )\n    },\n    uvUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-upload/components/uv-upload/uv-upload\" */ \"@/uni_modules/uv-upload/components/uv-upload/uv-upload.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--input/u--input\" */ \"@/uni_modules/uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var g1 = _vm.list.length\n  var m0 =\n    _vm.dateFormat(\n      new Date(Number(_vm.finishForm.finishTime)),\n      \"yyyy-MM-dd hh:mm\"\n    ) || \"请选择完成时间\"\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showFinishPopup = false\n      _vm.showSettlePopup = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showFilterPopup = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showFilterPopup = false\n    }\n    _vm.e3 = function ($event, status) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        status = _temp2.status\n      var _temp, _temp2\n      _vm.filterForm.payStatus =\n        _vm.filterForm.payStatus === status.value ? null : status.value\n    }\n    _vm.e4 = function ($event) {\n      _vm.showStartDatePicker = true\n    }\n    _vm.e5 = function ($event) {\n      _vm.showEndDatePicker = true\n    }\n    _vm.e6 = function ($event) {\n      _vm.showStartDatePicker = false\n    }\n    _vm.e7 = function ($event) {\n      _vm.showStartDatePicker = false\n    }\n    _vm.e8 = function ($event) {\n      _vm.showEndDatePicker = false\n    }\n    _vm.e9 = function ($event) {\n      _vm.showEndDatePicker = false\n    }\n    _vm.e10 = function ($event) {\n      _vm.modalShow = false\n    }\n    _vm.e11 = function ($event) {\n      _vm.showFinishPopup = false\n    }\n    _vm.e12 = function ($event) {\n      _vm.showFinishTimePicker = true\n    }\n    _vm.e13 = function ($event) {\n      _vm.showFinishTimePicker = false\n    }\n    _vm.e14 = function ($event) {\n      _vm.showFinishTimePicker = false\n    }\n    _vm.e15 = function ($event) {\n      _vm.showSettlePopup = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrder.vue?vue&type=script&lang=js&\"", "<template>\r\n  <page-meta\r\n    :page-style=\"\r\n      'overflow:' + (showFinishPopup || showSettlePopup ? 'hidden' : 'visible')\r\n    \"\r\n  ></page-meta>\r\n  <page-container\r\n    :show=\"showFinishPopup || showSettlePopup\"\r\n    @beforeleave=\"\r\n      showFinishPopup = false;\r\n      showSettlePopup = false;\r\n    \"\r\n  ></page-container>\r\n  <view class=\"workbench-wrap\">\r\n   \r\n\r\n    <!-- 搜索框 -->\r\n    <view class=\"search-container\">\r\n      <view class=\"search-box\">\r\n        <u-search\r\n          shape=\"square\"\r\n          v-model=\"keyWords\"\r\n          placeholder=\"搜索工单名称、客户名称、联系人\"\r\n          :showAction=\"true\"\r\n          actionText=\"搜索\"\r\n          @search=\"handleSearch\"\r\n          @custom=\"handleSearch\"\r\n        ></u-search>\r\n      </view>\r\n      <!-- 筛选按钮 -->\r\n      <view class=\"filter-btn\" @click=\"showFilterPopup = true\">\r\n        <u-icon name=\"list\" size=\"20\" color=\"#666\"></u-icon>\r\n        <text>筛选</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 工单tab栏 -->\r\n    <view class=\"order-tab-bar\">\r\n      <view\r\n        v-for=\"(tab, idx) in subList\"\r\n        :key=\"tab.value\"\r\n        :class=\"['tab-item', { active: currentStatus === idx }]\"\r\n        @tap=\"sectionChange(idx)\"\r\n      >\r\n        {{ tab.label }}\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 筛选弹窗 -->\r\n    <u-popup\r\n      :show=\"showFilterPopup\"\r\n      mode=\"bottom\"\r\n      @close=\"showFilterPopup = false\"\r\n      :closeable=\"true\"\r\n    >\r\n      <view class=\"filter-popup\">\r\n        <view class=\"popup-title\">筛选条件</view>\r\n        <view class=\"filter-content\">\r\n          <view class=\"filter-item\">\r\n            <view class=\"filter-label\">支付状态</view>\r\n            <view class=\"filter-options\">\r\n              <view\r\n                v-for=\"status in payStatus\"\r\n                :key=\"status.value\"\r\n                :class=\"['filter-option', { active: filterForm.payStatus === status.value }]\"\r\n                @click=\"filterForm.payStatus = filterForm.payStatus === status.value ? null : status.value\"\r\n              >\r\n                {{ status.label }}\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"filter-item\">\r\n            <view class=\"filter-label\">服务时间</view>\r\n            <view class=\"date-range\">\r\n              <u-input\r\n                v-model=\"filterForm.startDate\"\r\n                placeholder=\"开始日期\"\r\n                type=\"select\"\r\n                @click=\"showStartDatePicker = true\"\r\n              />\r\n              <text style=\"margin: 0 10rpx;\">至</text>\r\n              <u-input\r\n                v-model=\"filterForm.endDate\"\r\n                placeholder=\"结束日期\"\r\n                type=\"select\"\r\n                @click=\"showEndDatePicker = true\"\r\n              />\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"filter-footer\">\r\n          <u-button type=\"info\" plain @click=\"resetFilter\">重置</u-button>\r\n          <u-button type=\"primary\" @click=\"applyFilter\">确定</u-button>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n\r\n    <!-- 日期选择器 -->\r\n    <u-datetime-picker\r\n      v-model=\"filterForm.startDate\"\r\n      :show=\"showStartDatePicker\"\r\n      @cancel=\"showStartDatePicker = false\"\r\n      @confirm=\"showStartDatePicker = false\"\r\n      mode=\"date\"\r\n    ></u-datetime-picker>\r\n    <u-datetime-picker\r\n      v-model=\"filterForm.endDate\"\r\n      :show=\"showEndDatePicker\"\r\n      @cancel=\"showEndDatePicker = false\"\r\n      @confirm=\"showEndDatePicker = false\"\r\n      mode=\"date\"\r\n    ></u-datetime-picker>\r\n    <!-- 工单列表 -->\r\n    <view class=\"order-list\">\r\n      <view v-if=\"list.length === 0\" class=\"empty-tip\"> 暂无工单 </view>\r\n      <view\r\n        v-for=\"item in list\"\r\n        @click.stop=\"toDetail(item)\"\r\n        :key=\"item.id\"\r\n        class=\"order-card\"\r\n      >\r\n        <view class=\"order-header\">\r\n          <view class=\"order-title\">{{ item.objectName || \"--\" }}</view>\r\n          <view style=\"display: flex; gap: 8rpx\">\r\n            <view\r\n              class=\"order-status\"\r\n              :class=\"'status-' + item.objectStatus\"\r\n            >{{ statusMap[item.objectStatus] }}</view>\r\n            <view\r\n              v-if=\"item.payStatus !== null && item.payStatus !== undefined\"\r\n              class=\"order-status pay-status\"\r\n              :class=\"'pay-status-' + item.payStatus\"\r\n            >\r\n              {{ payStatusMap[item.payStatus] }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">服务类型：</view>\r\n          <view class=\"order-value\">{{ item.serverTypeName || \"--\" }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">服务客户：</view>\r\n          <view class=\"order-value\">{{ item.finalCustomer || \"--\" }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">联系人：</view>\r\n          <view class=\"order-value\">{{ item.contact || \"--\" }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">联系电话：</view>\r\n          <view class=\"order-value\">{{ item.contactPhone || \"--\" }}</view>\r\n          <u-icon\r\n            v-if=\"item.contactPhone\"\r\n            name=\"phone\"\r\n            size=\"20\"\r\n            color=\"#2979ff\"\r\n            @tap.stop=\"makePhoneCall(item.contactPhone)\"\r\n            style=\"margin-left: 10rpx; cursor: pointer;\"\r\n          ></u-icon>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">服务地址：</view>\r\n          <view class=\"order-value\">{{ item.distributionAddress || \"--\" }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">服务时间：</view>\r\n          <view class=\"order-value\">{{ item.serviceStartTime || \"--\" }} - {{ item.serviceEndTime || \"--\" }}</view>\r\n        </view>\r\n       \r\n        <view class=\"order-detail-row\" v-if=\"item.orderPrice\">\r\n          <view class=\"order-label\">金额：</view>\r\n          <view class=\"order-value price\">¥{{ item.orderPrice }}</view>\r\n        </view>\r\n\r\n        <!-- 操作区 -->\r\n        <view class=\"order-actions\">\r\n          <template v-if=\"item.objectStatus == 3\">\r\n            <u-button\r\n              type=\"primary\"\r\n              plain\r\n              class=\"action-btn primary\"\r\n              @tap.stop=\"handleStart(item)\"\r\n            >接单</u-button>\r\n          </template>\r\n          <template v-else-if=\"item.objectStatus == 1\">\r\n            <u-button\r\n              class=\"action-btn primary\"\r\n              plain\r\n              type=\"primary\"\r\n              @tap.stop=\"handleFinish(item)\"\r\n            >完成</u-button>\r\n          </template>\r\n          <template v-else-if=\"item.objectStatus == 2 && item.payStatus == null\">\r\n            <u-button\r\n              class=\"action-btn primary\"\r\n              plain\r\n              type=\"primary\"\r\n              @tap.stop=\"handleSettled(item)\"\r\n            >申请结算</u-button>\r\n          </template>\r\n        </view>\r\n      </view>\r\n      <uni-load-more\r\n        :status=\"list.length == page.total ? 'noMore' : 'loading'\"\r\n      ></uni-load-more>\r\n    </view>\r\n    <!-- 接单确认弹窗 -->\r\n    <u-modal\r\n      :show=\"modalShow\"\r\n      @confirm=\"confirm\"\r\n      ref=\"uModal\"\r\n      title=\"确认接单\"\r\n      content=\"确认接单吗？\"\r\n      showCancelButton\r\n      @cancel=\"modalShow = false\"\r\n      :asyncClose=\"true\"\r\n    ></u-modal>\r\n\r\n    <!-- 用户信息完善提示 -->\r\n    <u-modal\r\n      :show=\"show\"\r\n      content=\"你还未完善手机号,姓名等信息,请点击确认去完善\"\r\n      @confirm=\"toUserInfo\"\r\n    ></u-modal>\r\n\r\n    <!-- 完成工单弹窗 -->\r\n    <u-popup\r\n      :show=\"showFinishPopup\"\r\n      mode=\"bottom\"\r\n      @close=\"showFinishPopup = false\"\r\n      :closeable=\"true\"\r\n    >\r\n      <view class=\"finish-popup\">\r\n        <view class=\"popup-title\">完成工单</view>\r\n        <view class=\"popup-content\">\r\n          <u-form\r\n            labelPosition=\"top\"\r\n            labelWidth=\"auto\"\r\n            :model=\"finishForm\"\r\n            ref=\"finishForm\"\r\n          >\r\n            <u-form-item\r\n              borderBottom\r\n              labelPosition=\"left\"\r\n              label=\"完成时间\"\r\n              required\r\n            >\r\n              <view\r\n                style=\"\r\n                  display: flex;\r\n                  justify-content: flex-end;\r\n                  align-items: center;\r\n                \"\r\n              >\r\n                <text @click=\"showFinishTimePicker = true\"\r\n                  >{{\r\n                    dateFormat(\r\n                      new Date(Number(finishForm.finishTime)),\r\n                      \"yyyy-MM-dd hh:mm\"\r\n                    ) || \"请选择完成时间\"\r\n                  }}\r\n                  <u-icon label=\"uView\" size=\"40\" name=\"arrow-right\"></u-icon\r\n                ></text>\r\n              </view>\r\n              <u-datetime-picker\r\n                v-model=\"finishForm.finishTime\"\r\n                :show=\"showFinishTimePicker\"\r\n                @cancel=\"showFinishTimePicker = false\"\r\n                @confirm=\"showFinishTimePicker = false\"\r\n                mode=\"datetime\"\r\n                :visibleItemCount=\"5\"\r\n              ></u-datetime-picker>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"备注\">\r\n              <u-textarea\r\n                v-model=\"finishForm.remark\"\r\n                border=\"none\"\r\n                placeholder=\"请输入备注信息\"\r\n              ></u-textarea>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"上传图片/视频\">\r\n              <uv-upload\r\n                accept=\"media\"\r\n                @clickPreview=\"handleClickPreview\"\r\n                :fileList=\"finishForm.fileList\"\r\n                @afterRead=\"afterRead\"\r\n                @delete=\"handleDelete\"\r\n                multiple\r\n                :maxCount=\"9\"\r\n              >\r\n              </uv-upload>\r\n            </u-form-item>\r\n          </u-form>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <u-button type=\"primary\" @click=\"submitFinish\">提交</u-button>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n    <u-popup\r\n      :show=\"showSettlePopup\"\r\n      mode=\"bottom\"\r\n      @close=\"showSettlePopup = false\"\r\n      :closeable=\"true\"\r\n    >\r\n      <view class=\"finish-popup\">\r\n        <view class=\"popup-title\">申请结算</view>\r\n        <view class=\"popup-content\">\r\n          <u-form\r\n            labelPosition=\"top\"\r\n            labelWidth=\"auto\"\r\n            :model=\"settleForm\"\r\n            ref=\"settleForm\"\r\n          >\r\n            <!-- 结算金额 -->\r\n            <u-form-item borderBottom label=\"结算金额\">\r\n              <u--input\r\n                v-model=\"settleForm.totalPrice\"\r\n                placeholder=\"请输入结算金额\"\r\n                border=\"none\"\r\n                type=\"number\"\r\n              />\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"备注\">\r\n              <u-textarea\r\n                v-model=\"settleForm.applyContent\"\r\n                border=\"none\"\r\n                placeholder=\"请输入备注信息\"\r\n              ></u-textarea>\r\n            </u-form-item>\r\n          </u-form>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <u-button type=\"primary\" @click=\"submitSettle\">提交</u-button>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n  </view>\r\n</template>\r\n<script>\r\nimport { dateFormat } from \"../../utils/date\";\r\nimport http from \"../../http/api.js\";\r\n;\r\nexport default {\r\n  name: \"workerOrder\",\r\n  data() {\r\n    return {\r\n      keyWords: \"\",\r\n      currentStatus: 0,\r\n      dateFormat,\r\n      list: [],\r\n      showFinishPopup: false,\r\n      showFinishTimePicker: false,\r\n      modalShow: false,\r\n      finishForm: {\r\n        finishTime: Number(new Date()),\r\n        remark: \"\",\r\n        fileList: [],\r\n      },\r\n      page: {\r\n        size: 10,\r\n        current: 1,\r\n        total: 0,\r\n      },\r\n      subList: [\r\n        {\r\n          label: \"待接单\",\r\n          value: 3,\r\n        },\r\n        {\r\n          label: \"进行中\",\r\n          value: 1,\r\n        },\r\n        {\r\n          label: \"已完成\",\r\n          value: 2,\r\n        },\r\n      ],\r\n      payStatus: [\r\n        {\r\n          value: 0,\r\n          label: \"待审核\",\r\n        },\r\n        {\r\n          value: 1,\r\n          label: \"待付款\",\r\n        },\r\n        {\r\n          value: 2,\r\n          label: \"已付款\",\r\n        },\r\n        {\r\n          value: 3,\r\n          label: \"审核失败\",\r\n        },\r\n      ],\r\n      show: false,\r\n      showSettlePopup: false,\r\n      settleForm: {},\r\n      // 新增字段\r\n      summary: {\r\n        totalNum: 0,\r\n        waitNum: 0,\r\n        doNum: 0,\r\n        completeNum: 0,\r\n      },\r\n      statusMap: {\r\n        3: \"待接单\",\r\n        1: \"进行中\",\r\n        2: \"已完成\",\r\n      },\r\n      payStatusMap: {\r\n        0: \"待审核\",\r\n        1: \"待付款\",\r\n        2: \"已付款\",\r\n        3: \"审核失败\",\r\n      },\r\n      // 筛选相关\r\n      showFilterPopup: false,\r\n      showStartDatePicker: false,\r\n      showEndDatePicker: false,\r\n      filterForm: {\r\n        payStatus: null,\r\n        startDate: \"\",\r\n        endDate: \"\",\r\n      },\r\n    };\r\n  },\r\n  onReady() {\r\n    this.getList();\r\n    this.getTaskStatistics();\r\n  },\r\n  onShow() {\r\n    this.validatePhone();\r\n  },\r\n  onPullDownRefresh() {\r\n    this.refreshData();\r\n  },\r\n  onReachBottom() {\r\n    this.scrolltolower();\r\n  },\r\n  methods: {\r\n    // 获取工单统计数据\r\n    getTaskStatistics() {\r\n      this.$u.api.getTaskStatistics().then((data) => {\r\n        this.summary = data.data || {};\r\n      }).catch(() => {\r\n        // 如果接口不存在，使用默认值\r\n        this.summary = {\r\n          totalNum: 0,\r\n          waitNum: 0,\r\n          doNum: 0,\r\n          completeNum: 0,\r\n        };\r\n      });\r\n    },\r\n    // 刷新数据\r\n    refreshData() {\r\n      this.page.current = 1;\r\n      this.list = [];\r\n      this.getList();\r\n      this.getTaskStatistics();\r\n      uni.stopPullDownRefresh();\r\n    },\r\n    getList() {\r\n      const params = {\r\n        size: this.page.size,\r\n        current: this.page.current,\r\n        objectStatus: this.subList[this.currentStatus].value,\r\n        objectName: this.keyWords,\r\n      };\r\n\r\n      // 添加筛选条件\r\n      if (this.filterForm.payStatus !== null) {\r\n        params.payStatus = this.filterForm.payStatus;\r\n      }\r\n      if (this.filterForm.startDate) {\r\n        params.startDate = this.filterForm.startDate;\r\n      }\r\n      if (this.filterForm.endDate) {\r\n        params.endDate = this.filterForm.endDate;\r\n      }\r\n\r\n      this.$u.api\r\n        .getWorkerOrder(params)\r\n        .then((res) => {\r\n          this.list = [...this.list, ...res.data.records];\r\n          this.page.total = res.data.total;\r\n        });\r\n    },\r\n    scrolltolower() {\r\n      console.log(1111);\r\n\r\n      if (this.list.length == this.page.total) return;\r\n      this.page.current++;\r\n      this.getList();\r\n    },\r\n    sectionChange(index) {\r\n      this.currentStatus = index;\r\n      this.page.current = 1;\r\n      this.list = [];\r\n      this.getList();\r\n    },\r\n    handleSearch() {\r\n      this.list = [];\r\n      this.page.current = 1;\r\n      this.getList();\r\n    },\r\n    handleStart(item) {\r\n      this.currentItem = item;\r\n      this.modalShow = true;\r\n    },\r\n    confirm() {\r\n      this.$u.api.startWorkerOrder(this.currentItem.id).then((res) => {\r\n        this.modalShow = false;\r\n        this.page.current = 1;\r\n        this.list = [];\r\n        this.getList();\r\n      });\r\n    },\r\n    handleFinish(item) {\r\n      this.showFinishPopup = true;\r\n      this.currentItem = item;\r\n    },\r\n    afterRead(event) {\r\n      console.log(event);\r\n      const { file } = event;\r\n      const indexAll = this.finishForm.fileList.length;\r\n      file.forEach((item, index) => {\r\n        this.finishForm.fileList.push({\r\n          ...item,\r\n          status: \"uploading\",\r\n          message: \"上传中\",\r\n          // url: item.thumb,\r\n          index: indexAll + index,\r\n        });\r\n      });\r\n      file.forEach((item, index) => {\r\n        this.uploadFile(item.url, indexAll + index);\r\n      });\r\n    },\r\n    uploadFile(url, index) {\r\n      return new Promise((resolve) => {\r\n        const params = {\r\n          filePath: url,\r\n          name: \"file\",\r\n        };\r\n        http.upload(\"/blade-resource/attach/upload\", params).then((res) => {\r\n          this.finishForm.fileList.find((item) => item.index == index).status =\r\n            \"success\";\r\n          this.finishForm.fileList.find((item) => item.index == index).message =\r\n            \"\";\r\n          this.finishForm.fileList.find((item) => item.index == index).url =\r\n            res.data.link;\r\n          this.finishForm.fileList.find((item) => item.index == index).id =\r\n            res.data.id;\r\n        });\r\n      });\r\n    },\r\n    handleDelete({ file, index, name }) {\r\n      console.log(file, index, name);\r\n      this.finishForm.fileList.splice(index, 1);\r\n    },\r\n    handleClickPreview(url, lists, name) {\r\n      console.log(url, lists, name);\r\n    },\r\n\r\n    submitFinish() {\r\n      if (!this.finishForm.finishTime) {\r\n        this.$u.toast(\"请选择完成时间\");\r\n        return;\r\n      }\r\n      const formData = {\r\n        id: this.currentItem.id,\r\n        finishTime: this.dateFormat(\r\n          new Date(Number(this.finishForm.finishTime)),\r\n          \"yyyy-MM-dd hh:mm:ss\"\r\n        ),\r\n        completeRemark: this.finishForm.remark,\r\n        completeFiles:\r\n          this.finishForm.fileList &&\r\n          this.finishForm.fileList.map((item) => item.id).join(\",\"),\r\n      };\r\n      this.$u.api\r\n        .finishWorkerOrder(formData)\r\n        .then((res) => {\r\n          this.$u.toast(\"提交成功\");\r\n          this.showFinishPopup = false;\r\n          this.finishForm = {\r\n            finishTime: \"\",\r\n            remark: \"\",\r\n            fileList: [],\r\n          };\r\n          this.getList();\r\n        })\r\n        .catch((err) => {\r\n          this.$u.toast(err.message || \"提交失败\");\r\n        });\r\n    },\r\n    toDetail(item) {\r\n      uni.navigateTo({\r\n        url: \"/pages/wokerOrder/wokerOrderDetail?id=\" + item.id,\r\n      });\r\n    },\r\n    validatePhone() {\r\n      this.$u.api.userInfo().then((res) => {\r\n        ;\r\n\r\n        if (!res.data.phone) {\r\n          this.show = true;\r\n        } else {\r\n          console.log(res);\r\n          this.show = false;\r\n        }\r\n      });\r\n    },\r\n    toUserInfo() {\r\n      uni.navigateTo({\r\n        url: \"/pages/person/userInfo\",\r\n      });\r\n    },\r\n    handleSettled(item) {\r\n      this.showSettlePopup = true;\r\n      this.settleForm = {\r\n        objectId: item.id,\r\n        totalPrice: item.orderPrice,\r\n      };\r\n    },\r\n    submitSettle() {\r\n      this.$u.api.applySettlement(this.settleForm).then((res) => {\r\n        this.$u.toast(\"申请成功\");\r\n        this.showSettlePopup = false;\r\n        this.settleForm = {\r\n          objectId: \"\",\r\n          totalPrice: \"\",\r\n        };\r\n        this.list = [];\r\n        this.page.current = 1;\r\n        this.getList();\r\n      });\r\n    },\r\n    // 筛选相关方法\r\n    resetFilter() {\r\n      this.filterForm = {\r\n        payStatus: null,\r\n        startDate: \"\",\r\n        endDate: \"\",\r\n      };\r\n    },\r\n    applyFilter() {\r\n      this.showFilterPopup = false;\r\n      this.page.current = 1;\r\n      this.list = [];\r\n      this.getList();\r\n    },\r\n    // 拨打电话\r\n    makePhoneCall(phone) {\r\n      uni.makePhoneCall({\r\n        phoneNumber: phone,\r\n        success() {\r\n          console.log(\"拨打电话成功！\");\r\n        },\r\n        fail(err) {\r\n          console.log(\"拨打电话失败！\", err);\r\n        },\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.workbench-wrap {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  padding: 32rpx 0 0 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n// 工单概况统计卡片\r\n.summary-card {\r\n  margin: 32rpx 32rpx 24rpx 32rpx;\r\n  background: #fff;\r\n  border-radius: 24rpx;\r\n  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);\r\n  padding: 32rpx 24rpx 24rpx 24rpx;\r\n}\r\n\r\n.summary-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 24rpx;\r\n  color: #333;\r\n}\r\n\r\n.summary-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 24rpx;\r\n  color: #888;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #2d8cf0;\r\n}\r\n\r\n.stat-item.total .stat-value {\r\n  color: #2d8cf0;\r\n}\r\n\r\n.stat-item.pending .stat-value {\r\n  color: #ff9900;\r\n}\r\n\r\n.stat-item.processing .stat-value {\r\n  color: #19be6b;\r\n}\r\n\r\n.stat-item.finished .stat-value {\r\n  color: #909399;\r\n}\r\n\r\n// 搜索容器\r\n.search-container {\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 0 32rpx 16rpx 32rpx;\r\n  gap: 16rpx;\r\n}\r\n\r\n.search-box {\r\n  flex: 1;\r\n}\r\n\r\n.filter-btn {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 80rpx;\r\n  height: 68rpx;\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);\r\n\r\n  text {\r\n    font-size: 20rpx;\r\n    color: #666;\r\n    margin-top: 4rpx;\r\n  }\r\n}\r\n\r\n// Tab栏\r\n.order-tab-bar {\r\n  display: flex;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  margin: 0 32rpx 16rpx 32rpx;\r\n  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);\r\n  overflow: hidden;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 24rpx 0;\r\n  font-size: 28rpx;\r\n  color: #888;\r\n  background: #fff;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #2d8cf0;\r\n  font-weight: bold;\r\n  background: #e6f7ff;\r\n}\r\n\r\n// 工单列表\r\n.order-list {\r\n  margin: 0 32rpx;\r\n}\r\n\r\n.order-card {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.04);\r\n  margin-bottom: 24rpx;\r\n  padding: 24rpx 20rpx 16rpx 20rpx;\r\n  transition: box-shadow 0.2s;\r\n}\r\n\r\n.order-card:hover {\r\n  box-shadow: 0 8rpx 32rpx 0 rgba(45, 140, 240, 0.08);\r\n}\r\n\r\n.order-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.order-title {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.order-status {\r\n  font-size: 24rpx;\r\n  padding: 4rpx 16rpx;\r\n  border-radius: 16rpx;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.order-status.status-3 {\r\n  color: #ff9900;\r\n  background: #fff7e6;\r\n}\r\n\r\n.order-status.status-1 {\r\n  color: #19be6b;\r\n  background: #e6ffed;\r\n}\r\n\r\n.order-status.status-2 {\r\n  color: #909399;\r\n  background: #f4f4f5;\r\n}\r\n\r\n.pay-status {\r\n  font-size: 22rpx;\r\n}\r\n\r\n.pay-status.pay-status-0 {\r\n  color: #ff9900;\r\n  background: #fff7e6;\r\n}\r\n\r\n.pay-status.pay-status-1 {\r\n  color: #f56c6c;\r\n  background: #fef0f0;\r\n}\r\n\r\n.pay-status.pay-status-2 {\r\n  color: #19be6b;\r\n  background: #e6ffed;\r\n}\r\n\r\n.pay-status.pay-status-3 {\r\n  color: #909399;\r\n  background: #f4f4f5;\r\n}\r\n\r\n.order-detail-row {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  font-size: 26rpx;\r\n  color: #555;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.order-label {\r\n  min-width: 120rpx;\r\n  color: #888;\r\n  font-weight: 400;\r\n}\r\n\r\n.order-value {\r\n  flex: 1;\r\n  color: #333;\r\n  word-break: break-all;\r\n}\r\n\r\n.order-value.price {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n}\r\n\r\n.order-actions {\r\n  display: flex;\r\n  gap: 24rpx;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 18rpx;\r\n  padding-top: 18rpx;\r\n  justify-content: flex-end;\r\n  background: #fff;\r\n}\r\n\r\n.action-btn {\r\n  min-width: 120rpx;\r\n  padding: 0 32rpx;\r\n  height: 56rpx;\r\n  line-height: 56rpx;\r\n  border: none;\r\n  border-radius: 32rpx;\r\n  background: #f5f7fa;\r\n  color: #2d8cf0;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  margin: 0;\r\n  outline: none;\r\n  box-shadow: 0 2rpx 8rpx 0 rgba(45, 140, 240, 0.04);\r\n  transition: background 0.2s, color 0.2s;\r\n}\r\n\r\n.action-btn.primary {\r\n  background: linear-gradient(90deg, #2d8cf0 0%, #57a3f3 100%);\r\n  color: #fff;\r\n}\r\n\r\n.action-btn:active {\r\n  opacity: 0.85;\r\n}\r\n\r\n.empty-tip {\r\n  text-align: center;\r\n  color: #bbb;\r\n  font-size: 28rpx;\r\n  margin: 64rpx 0;\r\n}\r\n\r\n// 筛选弹窗\r\n.filter-popup {\r\n  padding: 30rpx;\r\n\r\n  .popup-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    margin-bottom: 30rpx;\r\n  }\r\n}\r\n\r\n.filter-content {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.filter-item {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.filter-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.filter-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16rpx;\r\n}\r\n\r\n.filter-option {\r\n  padding: 12rpx 24rpx;\r\n  background: #f5f7fa;\r\n  border-radius: 32rpx;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.filter-option.active {\r\n  background: #2d8cf0;\r\n  color: #fff;\r\n}\r\n\r\n.date-range {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n}\r\n\r\n.filter-footer {\r\n  display: flex;\r\n  gap: 24rpx;\r\n\r\n  .u-button {\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n// 完成弹窗\r\n.finish-popup {\r\n  height: 80vh;\r\n  overflow-y: auto;\r\n  position: relative;\r\n\r\n  .popup-title {\r\n    padding-top: 20rpx;\r\n    position: sticky;\r\n    background-color: #fff;\r\n    top: 0;\r\n    z-index: 10;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    text-align: center;\r\n  }\r\n\r\n  .popup-content {\r\n    padding: 30rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .popup-footer {\r\n    padding: 20rpx 0;\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 20rpx;\r\n    right: 20rpx;\r\n    width: auto;\r\n  }\r\n}\r\n\r\n::v-deep input {\r\n  text-align: right !important;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrder.vue?vue&type=style&index=0&id=0ec3f686&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrder.vue?vue&type=style&index=0&id=0ec3f686&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759141817772\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}