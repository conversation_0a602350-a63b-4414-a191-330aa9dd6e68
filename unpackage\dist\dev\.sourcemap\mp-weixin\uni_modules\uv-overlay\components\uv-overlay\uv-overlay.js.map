{"version": 3, "sources": ["webpack:///D:/project/vt-unih5-order/uni_modules/uv-overlay/components/uv-overlay/uv-overlay.vue?2d4a", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-overlay/components/uv-overlay/uv-overlay.vue?df6f", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-overlay/components/uv-overlay/uv-overlay.vue?84d0", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-overlay/components/uv-overlay/uv-overlay.vue?4239", "uni-app:///uni_modules/uv-overlay/components/uv-overlay/uv-overlay.vue", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-overlay/components/uv-overlay/uv-overlay.vue?99f3", "webpack:///D:/project/vt-unih5-order/uni_modules/uv-overlay/components/uv-overlay/uv-overlay.vue?0de2"], "names": ["name", "emits", "mixins", "watch", "show", "computed", "overlayStyle", "position", "top", "left", "right", "zIndex", "bottom", "methods", "clickHandler", "clear"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAqoB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACezpB;AACA;AACA;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,eAYA;EACAA;EACAC;EACAC;EACAC;IACAC,6BAQA;EACA;EACAC;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAAguC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACApvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uv-overlay/components/uv-overlay/uv-overlay.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uv-overlay.vue?vue&type=template&id=9ba9e068&scoped=true&\"\nvar renderjs\nimport script from \"./uv-overlay.vue?vue&type=script&lang=js&\"\nexport * from \"./uv-overlay.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uv-overlay.vue?vue&type=style&index=0&id=9ba9e068&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9ba9e068\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uv-overlay/components/uv-overlay/uv-overlay.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-overlay.vue?vue&type=template&id=9ba9e068&scoped=true&\"", "var components\ntry {\n  components = {\n    uvTransition: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-transition/components/uv-transition/uv-transition\" */ \"@/uni_modules/uv-transition/components/uv-transition/uv-transition.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-overlay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-overlay.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<uv-transition\r\n\t  :show=\"show\"\r\n\t\tmode=\"fade\"\r\n\t  custom-class=\"uv-overlay\"\r\n\t  :duration=\"duration\"\r\n\t  :custom-style=\"overlayStyle\"\r\n\t  @click=\"clickHandler\"\r\n\t\************************=\"clear\"\r\n\t>\r\n\t\t<slot />\r\n\t</uv-transition>\r\n</template>\r\n\r\n<script>\r\n\timport mpMixin from '@/uni_modules/uv-ui-tools/libs/mixin/mpMixin.js'\r\n\timport mixin from '@/uni_modules/uv-ui-tools/libs/mixin/mixin.js'\r\n\timport props from './props.js';\r\n\r\n\t/**\r\n\t * overlay 遮罩\r\n\t * @description 创建一个遮罩层，用于强调特定的页面元素，并阻止用户对遮罩下层的内容进行操作，一般用于弹窗场景\r\n\t * @tutorial https://www.uvui.cn/components/overlay.html\r\n\t * @property {Boolean}\t\t\tshow\t\t是否显示遮罩（默认 false ）\r\n\t * @property {String | Number}\tzIndex\t\tzIndex 层级（默认 10070 ）\r\n\t * @property {String | Number}\tduration\t动画时长，单位毫秒（默认 300 ）\r\n\t * @property {String | Number}\topacity\t\t不透明度值，当做rgba的第四个参数 （默认 0.5 ）\r\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\r\n\t * @event {Function} click 点击遮罩发送事件\r\n\t * @example <uv-overlay :show=\"show\" @click=\"show = false\"></uv-overlay>\r\n\t */\r\n\texport default {\r\n\t\tname: \"uv-overlay\",\r\n\t\temits: ['click'],\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\twatch: {\r\n\t\t\tshow(newVal){\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif(newVal){\r\n\t\t\t\t\tdocument.querySelector('body').style.overflow = 'hidden';\r\n\t\t\t\t}else{\r\n\t\t\t\t\tdocument.querySelector('body').style.overflow = '';\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\toverlayStyle() {\r\n\t\t\t\tconst style = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\tzIndex: this.zIndex,\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t'background-color': `rgba(0, 0, 0, ${this.opacity})`\r\n\t\t\t\t}\r\n\t\t\t\treturn this.$uv.deepMerge(style, this.$uv.addStyle(this.customStyle))\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tclickHandler() {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t},\r\n\t\t\tclear() {}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" scoped>\n/* #ifndef APP-NVUE */\r\n$uv-overlay-top:0 !default;\n$uv-overlay-left:0 !default;\n$uv-overlay-width:100% !default;\n$uv-overlay-height:100% !default;\n$uv-overlay-background-color:rgba(0, 0, 0, .7) !default;\n.uv-overlay {\n\tposition: fixed;\n\ttop:$uv-overlay-top;\n\tleft:$uv-overlay-left;\n\twidth: $uv-overlay-width;\n\theight:$uv-overlay-height;\n\tbackground-color:$uv-overlay-background-color;\n}\r\n/* #endif */\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-overlay.vue?vue&type=style&index=0&id=9ba9e068&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-overlay.vue?vue&type=style&index=0&id=9ba9e068&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759141819046\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}