<view class="{{['u-textarea','data-v-c27e5be4',textareaClass]}}" style="{{$root.s0}}"><textarea class="u-textarea__field data-v-c27e5be4" style="{{'height:'+($root.g0)+';'}}" placeholder="{{placeholder}}" placeholder-style="{{$root.g1}}" placeholder-class="{{placeholderClass}}" disabled="{{disabled}}" focus="{{focus}}" autoHeight="{{autoHeight}}" fixed="{{fixed}}" cursorSpacing="{{cursorSpacing}}" cursor="{{cursor}}" showConfirmBar="{{showConfirmBar}}" selectionStart="{{selectionStart}}" selectionEnd="{{selectionEnd}}" adjustPosition="{{adjustPosition}}" disableDefaultPadding="{{disableDefaultPadding}}" holdKeyboard="{{holdKeyboard}}" maxlength="{{maxlength}}" confirmType="{{confirmType}}" ignoreCompositionEvent="{{ignoreCompositionEvent}}" data-event-opts="{{[['focus',[['onFocus',['$event']]]],['blur',[['onBlur',['$event']]]],['linechange',[['onLinechange',['$event']]]],['input',[['onInput',['$event']]]],['confirm',[['onConfirm',['$event']]]],['keyboardheightchange',[['onKeyboardheightchange',['$event']]]]]}}" value="{{innerValue}}" bindfocus="__e" bindblur="__e" bindlinechange="__e" bindinput="__e" bindconfirm="__e" bindkeyboardheightchange="__e"></textarea><block wx:if="{{count}}"><text class="u-textarea__count data-v-c27e5be4" style="{{'background-color:'+(disabled?'transparent':'#fff')+';'}}">{{$root.g2+"/"+maxlength}}</text></block></view>