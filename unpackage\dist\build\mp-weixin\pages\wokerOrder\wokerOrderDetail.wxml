<page-meta page-style="{{'overflow:'+(showFinishPopup||showSignDrawer?'hidden':'visible')}}" class="data-v-2138eec6"></page-meta><page-container show="{{showFinishPopup||showSignDrawer}}" data-event-opts="{{[['beforeleave',[['e0',['$event']]]]]}}" bindbeforeleave="__e" class="data-v-2138eec6"></page-container><view class="data-v-2138eec6"><view class="order-detail data-v-2138eec6"><view class="card shadow data-v-2138eec6"><view class="card-header data-v-2138eec6"><view class="header-icon data-v-2138eec6"><uni-icons vue-id="255f6022-1" type="person" size="20" color="#000" class="data-v-2138eec6" bind:__l="__l"></uni-icons></view><text class="card-title data-v-2138eec6">服务客户信息</text></view><view class="card-content data-v-2138eec6"><view class="info-row data-v-2138eec6"><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">客户名称</text><text class="value data-v-2138eec6">{{detailForm.customerName}}</text></view><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">联系人</text><text class="value data-v-2138eec6">{{detailForm.contact}}</text></view></view><view class="info-row data-v-2138eec6"><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">联系电话</text><view style="display:flex;align-items:center;" class="data-v-2138eec6"><text class="value phone data-v-2138eec6">{{detailForm.contactPhone}}</text><block wx:if="{{detailForm.contactPhone}}"><u-icon vue-id="255f6022-2" name="phone" color="#2979ff" size="20" data-event-opts="{{[['^click',[['callPhone',['$0'],['detailForm.contactPhone']]]]]}}" bind:click="__e" class="data-v-2138eec6" bind:__l="__l"></u-icon></block></view></view><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">地址</text><text class="value data-v-2138eec6">{{detailForm.distributionAddress}}</text></view></view></view></view><view class="card shadow data-v-2138eec6"><view class="card-header data-v-2138eec6"><view class="header-icon data-v-2138eec6"><uni-icons vue-id="255f6022-3" type="compose" size="20" color="#000" class="data-v-2138eec6" bind:__l="__l"></uni-icons></view><text class="card-title data-v-2138eec6">工单信息</text></view><view class="card-content data-v-2138eec6"><view class="info-row data-v-2138eec6"><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">工单名称</text><text class="value tag data-v-2138eec6">{{detailForm.objectName}}</text></view><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">服务类型</text><text class="value tag data-v-2138eec6">{{detailForm.serverTypeName}}</text></view></view><view class="info-row single data-v-2138eec6"><view class="info-item full data-v-2138eec6"><text class="label data-v-2138eec6">服务时间</text><text class="value time data-v-2138eec6">{{detailForm.serviceStartTime+" -\n              "+detailForm.serviceEndTime}}</text></view></view><view class="info-row data-v-2138eec6"><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">发布人</text><text class="value data-v-2138eec6">{{detailForm.projectLeaderName}}</text></view><view class="info-item full data-v-2138eec6"><text class="label data-v-2138eec6">指派工程师</text><text class="value data-v-2138eec6">{{detailForm.handleUserName}}</text></view></view><view class="info-row single data-v-2138eec6"><view class="info-item full data-v-2138eec6"><text class="label data-v-2138eec6">SLA</text><text class="value code data-v-2138eec6">{{detailForm.SLATypeName||"-"}}</text></view></view><view class="info-row single data-v-2138eec6"><view class="info-item full data-v-2138eec6"><text class="label data-v-2138eec6">工单描述</text><text class="value desc data-v-2138eec6">{{detailForm.taskDescription}}</text></view></view><view class="info-row single data-v-2138eec6"><block wx:for="{{detailForm.fileList}}" wx:for-item="item" wx:for-index="__i0__"><view class="info-item full data-v-2138eec6"><text class="label data-v-2138eec6">工单附件</text><block wx:for="{{detailForm.fileList}}" wx:for-item="item" wx:for-index="__i1__"><text class="value file-link data-v-2138eec6">{{item.originalName}}</text></block></view></block></view></view></view><view class="card shadow data-v-2138eec6"><view class="card-header data-v-2138eec6"><view class="header-icon data-v-2138eec6"><uni-icons vue-id="255f6022-4" type="calendar" size="20" color="#000" class="data-v-2138eec6" bind:__l="__l"></uni-icons></view><text class="card-title data-v-2138eec6">工单执行</text></view><view class="timeline-container data-v-2138eec6"><view class="timeline data-v-2138eec6"><block wx:for="{{detailForm.milestoneVOList}}" wx:for-item="item" wx:for-index="index"><view class="timeline-item data-v-2138eec6"><view class="timeline-dot active data-v-2138eec6"></view><view class="timeline-line data-v-2138eec6"></view><view class="timeline-content data-v-2138eec6"><view class="timeline-header data-v-2138eec6"><text class="timeline-title data-v-2138eec6">{{item.handleContent}}</text><text class="timeline-time data-v-2138eec6">{{item.createTime}}</text></view></view></view></block></view></view></view><block wx:if="{{detailForm.objectStatus==2||detailForm.objectStatus==3}}"><view class="card shadow data-v-2138eec6"><view class="card-header data-v-2138eec6"><view class="header-icon data-v-2138eec6"><uni-icons vue-id="255f6022-5" type="checkmarkempty" size="20" color="#000" class="data-v-2138eec6" bind:__l="__l"></uni-icons></view><text class="card-title data-v-2138eec6">完工报告</text></view><view class="card-content data-v-2138eec6"><block wx:for="{{$root.l1}}" wx:for-item="report" wx:for-index="index" wx:key="index"><view class="completion-report data-v-2138eec6"><view class="report-header data-v-2138eec6"><text class="report-title data-v-2138eec6">{{report.$orig.handleName||'工程师 '+(index+1)}}</text><text class="report-time data-v-2138eec6">{{report.$orig.serviceStartTime||'-'}}</text></view><block wx:if="{{detailForm.isNeedSign==1}}"><view class="collapsible-section data-v-2138eec6"><view data-event-opts="{{[['tap',[['toggleSection',['signin',index]]]]]}}" class="section-header data-v-2138eec6" bindtap="__e"><view class="section-title data-v-2138eec6"><uni-icons vue-id="{{'255f6022-6-'+index}}" type="location" size="16" color="#2979ff" class="data-v-2138eec6" bind:__l="__l"></uni-icons><text class="section-text data-v-2138eec6">签到信息</text></view><view class="{{['section-arrow','data-v-2138eec6',(report.m0)?'expanded':'']}}"><uni-icons vue-id="{{'255f6022-7-'+index}}" type="arrowdown" size="14" color="#666" class="data-v-2138eec6" bind:__l="__l"></uni-icons></view></view><view hidden="{{!(report.m1)}}" class="section-content data-v-2138eec6"><view class="info-row data-v-2138eec6"><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">签到时间</text><text class="value time data-v-2138eec6">{{report.$orig.signTime||report.$orig.serviceStartTime||'-'}}</text></view><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">签到地址</text><text class="value data-v-2138eec6">{{report.$orig.signAddress||'现场签到'}}</text></view></view><block wx:if="{{report.g0}}"><view class="info-row single data-v-2138eec6"><view class="info-item full data-v-2138eec6"><text class="label data-v-2138eec6">签到图片</text><view class="photo-grid data-v-2138eec6"><block wx:for="{{report.l0}}" wx:for-item="photo" wx:for-index="photoIndex" wx:key="photoIndex"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({photo,report:report.$orig})}}" class="photo-item data-v-2138eec6" bindtap="__e"><image class="photo-image data-v-2138eec6" src="{{photo}}" mode="aspectFill"></image></view></block></view></view></view></block></view></view></block><view class="collapsible-section data-v-2138eec6"><view data-event-opts="{{[['tap',[['toggleSection',['complete',index]]]]]}}" class="section-header data-v-2138eec6" bindtap="__e"><view class="section-title data-v-2138eec6"><uni-icons vue-id="{{'255f6022-8-'+index}}" type="checkmarkempty" size="16" color="#4CAF50" class="data-v-2138eec6" bind:__l="__l"></uni-icons><text class="section-text data-v-2138eec6">完成信息</text></view><view class="{{['section-arrow','data-v-2138eec6',(report.m2)?'expanded':'']}}"><uni-icons vue-id="{{'255f6022-9-'+index}}" type="arrowdown" size="14" color="#666" class="data-v-2138eec6" bind:__l="__l"></uni-icons></view></view><view hidden="{{!(report.m3)}}" class="section-content data-v-2138eec6"><view class="info-row data-v-2138eec6"><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">服务开始时间</text><text class="value time data-v-2138eec6">{{report.$orig.serviceStartTime||"-"}}</text></view><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">服务结束时间</text><text class="value time data-v-2138eec6">{{report.$orig.serviceEndTime||"-"}}</text></view></view><view class="info-row data-v-2138eec6"><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">实际工时</text><text class="value data-v-2138eec6">{{(report.$orig.useTimes||"-")+"小时"}}</text></view><view class="info-item data-v-2138eec6"><text class="label data-v-2138eec6">完成情况</text><text class="value tag data-v-2138eec6">{{report.$orig.completeStatusName||"-"}}</text></view></view><block wx:if="{{report.$orig.serviceReorder}}"><view class="info-row single data-v-2138eec6"><view class="info-item full data-v-2138eec6"><text class="label data-v-2138eec6">服务复盘</text><text class="value desc data-v-2138eec6">{{report.$orig.serviceReorder}}</text></view></view></block><block wx:if="{{report.$orig.completeRemark}}"><view class="info-row single data-v-2138eec6"><view class="info-item full data-v-2138eec6"><text class="label data-v-2138eec6">备注</text><text class="value desc data-v-2138eec6">{{report.$orig.completeRemark}}</text></view></view></block><block wx:if="{{report.g1}}"><view class="info-row single data-v-2138eec6"><view class="info-item full data-v-2138eec6"><text class="label data-v-2138eec6">现场图片</text><view class="photo-grid data-v-2138eec6"><block wx:for="{{report.$orig.workOrderPhotoList}}" wx:for-item="photo" wx:for-index="photoIndex" wx:key="photoIndex"><view data-event-opts="{{[['tap',[['previewImage',['$0','$1'],[[['detailForm.sealContractObjectResultVOList','',index],['workOrderPhotoList','',photoIndex,'url']],[['detailForm.sealContractObjectResultVOList','',index,'workOrderPhotoList']]]]]]]}}" class="photo-item data-v-2138eec6" bindtap="__e"><image class="photo-image data-v-2138eec6" src="{{photo.link}}" mode="aspectFill"></image></view></block></view></view></view></block><block wx:if="{{report.g2}}"><view class="info-row single data-v-2138eec6"><view class="info-item full data-v-2138eec6"><text class="label data-v-2138eec6">处理结果图片</text><view class="photo-grid data-v-2138eec6"><block wx:for="{{report.$orig.handleResultPhotoList}}" wx:for-item="photo" wx:for-index="photoIndex" wx:key="photoIndex"><view data-event-opts="{{[['tap',[['previewImage',['$0','$1'],[[['detailForm.sealContractObjectResultVOList','',index],['handleResultPhotoList','',photoIndex,'url']],[['detailForm.sealContractObjectResultVOList','',index,'handleResultPhotoList']]]]]]]}}" class="photo-item data-v-2138eec6" bindtap="__e"><image class="photo-image data-v-2138eec6" src="{{photo.link}}" mode="aspectFill"></image></view></block></view></view></view></block></view></view><block wx:if="{{index<report.g3-1}}"><view class="report-divider data-v-2138eec6"></view></block></view></block></view></view></block><block wx:if="{{showActionButtons}}"><view class="bottom-placeholder data-v-2138eec6"></view></block></view><block wx:if="{{showActionButtons}}"><view class="fixed-action-bar data-v-2138eec6"><view class="action-buttons data-v-2138eec6"><block wx:if="{{detailForm.objectStatus==3}}"><u-button class="action-btn primary data-v-2138eec6" vue-id="255f6022-10" type="primary" plain="{{true}}" data-event-opts="{{[['^tap',[['handleAccept']]]]}}" bind:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">接单</u-button></block><block wx:else><block wx:if="{{detailForm.objectStatus==1&&detailForm.isNeedSign==1&&detailForm.isSign==0}}"><u-button class="action-btn data-v-2138eec6" vue-id="255f6022-11" type="primary" icon="map" plain="{{true}}" data-event-opts="{{[['^tap',[['handleSign']]]]}}" bind:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">签到</u-button></block><block wx:if="{{detailForm.isNeedSign==1?detailForm.isSign==1:detailForm.objectStatus==1}}"><u-button class="action-btn primary data-v-2138eec6" vue-id="255f6022-12" type="primary" plain="{{true}}" data-event-opts="{{[['^tap',[['handleFinish']]]]}}" bind:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">完成</u-button></block></block></view></view></block><u-modal vue-id="255f6022-13" show="{{acceptOrderModalShow}}" title="确认接单" content="确认接单吗？" showCancelButton="{{true}}" asyncClose="{{true}}" data-ref="uModal" data-event-opts="{{[['^confirm',[['acceptOrderConfirm']]],['^cancel',[['e2']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-2138eec6 vue-ref" bind:__l="__l"></u-modal><u-popup style="z-index:9999;" vue-id="255f6022-14" show="{{showSignDrawer}}" mode="bottom" closeOnClickOverlay="{{true}}" mask-click="{{true}}" background="#fff" data-event-opts="{{[['^close',[['e3']]]]}}" bind:close="__e" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}"><view style="padding:32rpx 24rpx;" class="data-v-2138eec6"><view style="font-size:32rpx;font-weight:bold;margin-bottom:24rpx;" class="data-v-2138eec6">签到</view><view style="margin-bottom:24rpx;" class="data-v-2138eec6"><view style="font-size:28rpx;margin-bottom:8rpx;" class="data-v-2138eec6">选择位置</view><u-button vue-id="{{('255f6022-15')+','+('255f6022-14')}}" type="primary" icon="map" loading="{{locationLoading}}" loadingText="正在获取位置..." plain="{{true}}" data-event-opts="{{[['^tap',[['chooseLocation']]]]}}" bind:tap="__e" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}">{{''+(signAddress?signAddress:"点击获取位置")+''}}</u-button></view><view style="margin-bottom:24rpx;" class="data-v-2138eec6"><view style="font-size:28rpx;margin-bottom:8rpx;" class="data-v-2138eec6">上传照片</view><uv-upload vue-id="{{('255f6022-16')+','+('255f6022-14')}}" accept="media" fileList="{{signPhotoUrl}}" multiple="{{true}}" maxCount="{{9}}" data-event-opts="{{[['^clickPreview',[['handleClickPreview']]],['^afterRead',[['afterReadSign']]],['^delete',[['handleDeleteSign']]]]}}" bind:clickPreview="__e" bind:afterRead="__e" bind:delete="__e" class="data-v-2138eec6" bind:__l="__l"></uv-upload></view><u-button vue-id="{{('255f6022-17')+','+('255f6022-14')}}" type="primary" data-event-opts="{{[['^tap',[['submitSign']]]]}}" bind:tap="__e" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}">确认签到</u-button></view></u-popup><u-popup vue-id="255f6022-18" show="{{showFinishPopup}}" mode="bottom" closeable="{{true}}" data-event-opts="{{[['^close',[['e4']]]]}}" bind:close="__e" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}"><view class="finish-popup data-v-2138eec6"><view class="popup-title data-v-2138eec6">完成工单</view><view class="popup-content data-v-2138eec6"><u-form vue-id="{{('255f6022-19')+','+('255f6022-18')}}" labelPosition="top" labelWidth="auto" model="{{finishForm}}" data-ref="finishForm" class="data-v-2138eec6 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('255f6022-20')+','+('255f6022-19')}}" borderBottom="{{true}}" labelPosition="left" label="服务开始时间" required="{{true}}" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}"><view style="display:flex;justify-content:flex-end;align-items:center;" class="data-v-2138eec6"><text data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" bindtap="__e" class="data-v-2138eec6">{{$root.m4+''}}<u-icon vue-id="{{('255f6022-21')+','+('255f6022-20')}}" label="uView" size="40" name="arrow-right" class="data-v-2138eec6" bind:__l="__l"></u-icon></text></view><u-datetime-picker vue-id="{{('255f6022-22')+','+('255f6022-20')}}" show="{{shiwServiceStartTimePicker}}" mode="datetime" visibleItemCount="{{5}}" value="{{finishForm.serviceStartTime}}" data-event-opts="{{[['^cancel',[['e6']]],['^confirm',[['e7']]],['^input',[['__set_model',['$0','serviceStartTime','$event',[]],['finishForm']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-2138eec6" bind:__l="__l"></u-datetime-picker></u-form-item><u-form-item vue-id="{{('255f6022-23')+','+('255f6022-19')}}" borderBottom="{{true}}" labelPosition="left" label="服务结束时间" required="{{true}}" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}"><view style="display:flex;justify-content:flex-end;align-items:center;" class="data-v-2138eec6"><text data-event-opts="{{[['tap',[['e8',['$event']]]]]}}" bindtap="__e" class="data-v-2138eec6">{{$root.m5+''}}<u-icon vue-id="{{('255f6022-24')+','+('255f6022-23')}}" label="uView" size="40" name="arrow-right" class="data-v-2138eec6" bind:__l="__l"></u-icon></text></view><u-datetime-picker vue-id="{{('255f6022-25')+','+('255f6022-23')}}" show="{{shiwServiceEndTimePicker}}" mode="datetime" visibleItemCount="{{5}}" value="{{finishForm.serviceEndTime}}" data-event-opts="{{[['^cancel',[['e9']]],['^confirm',[['e10']]],['^input',[['__set_model',['$0','serviceEndTime','$event',[]],['finishForm']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-2138eec6" bind:__l="__l"></u-datetime-picker></u-form-item><u-form-item vue-id="{{('255f6022-26')+','+('255f6022-19')}}" labelPosition="left" label="实际使用工时" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('255f6022-27')+','+('255f6022-26')}}" placeholder="请输入实际使用工时" border="{{false}}" type="digit" value="{{finishForm.useTimes}}" data-event-opts="{{[['^input',[['__set_model',['$0','useTimes','$event',[]],['finishForm']]]]]}}" class="data-v-2138eec6" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('255f6022-28')+','+('255f6022-19')}}" labelPosition="left" label="完成情况" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}"><dic-picker bind:input="__e" vue-id="{{('255f6022-29')+','+('255f6022-28')}}" dicUrl="/blade-system/dict-biz/dictionary?code=completeType" placeholder="请选择完成情况" value="{{finishForm.completeStatus}}" data-event-opts="{{[['^input',[['__set_model',['$0','completeStatus','$event',[]],['finishForm']]]]]}}" class="data-v-2138eec6" bind:__l="__l"></dic-picker></u-form-item><u-form-item vue-id="{{('255f6022-30')+','+('255f6022-19')}}" borderBottom="{{true}}" label="现场图" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}"><uv-upload vue-id="{{('255f6022-31')+','+('255f6022-30')}}" accept="media" fileList="{{finishForm.workOrderPhotos}}" multiple="{{true}}" maxCount="{{9}}" data-event-opts="{{[['^clickPreview',[['handleClickPreview']]],['^afterRead',[['afterReadForXC']]],['^delete',[['handleDeleteForXC']]]]}}" bind:clickPreview="__e" bind:afterRead="__e" bind:delete="__e" class="data-v-2138eec6" bind:__l="__l"></uv-upload></u-form-item><u-form-item vue-id="{{('255f6022-32')+','+('255f6022-19')}}" borderBottom="{{true}}" label="处理结果图" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}"><uv-upload vue-id="{{('255f6022-33')+','+('255f6022-32')}}" accept="media" fileList="{{finishForm.handleResultPhotos}}" multiple="{{true}}" maxCount="{{9}}" data-event-opts="{{[['^clickPreview',[['handleClickPreview']]],['^afterRead',[['afterReadForFinish']]],['^delete',[['handleDeleteForFinish']]]]}}" bind:clickPreview="__e" bind:afterRead="__e" bind:delete="__e" class="data-v-2138eec6" bind:__l="__l"></uv-upload></u-form-item><u-form-item vue-id="{{('255f6022-34')+','+('255f6022-19')}}" borderBottom="{{true}}" label="服务复盘" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('255f6022-35')+','+('255f6022-34')}}" border="none" placeholder="请输入服务复盘" value="{{finishForm.serviceReorder}}" data-event-opts="{{[['^input',[['__set_model',['$0','serviceReorder','$event',[]],['finishForm']]]]]}}" class="data-v-2138eec6" bind:__l="__l"></u-textarea></u-form-item><u-form-item vue-id="{{('255f6022-36')+','+('255f6022-19')}}" borderBottom="{{true}}" label="备注" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('255f6022-37')+','+('255f6022-36')}}" border="none" placeholder="请输入备注信息" value="{{finishForm.completeRemark}}" data-event-opts="{{[['^input',[['__set_model',['$0','completeRemark','$event',[]],['finishForm']]]]]}}" class="data-v-2138eec6" bind:__l="__l"></u-textarea></u-form-item></u-form></view><view class="popup-footer data-v-2138eec6"><u-button vue-id="{{('255f6022-38')+','+('255f6022-18')}}" type="primary" data-event-opts="{{[['^click',[['submitFinish']]]]}}" bind:click="__e" class="data-v-2138eec6" bind:__l="__l" vue-slots="{{['default']}}">提交</u-button></view></view></u-popup></view>