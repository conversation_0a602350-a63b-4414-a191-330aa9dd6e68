<view class="uv-upload data-v-e6e7992a" style="{{$root.s0}}"><view class="uv-upload__wrap data-v-e6e7992a"><block wx:if="{{previewImage}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uv-upload__wrap__preview data-v-e6e7992a"><block wx:if="{{item.$orig.isImage||item.$orig.type&&item.$orig.type==='image'}}"><image class="uv-upload__wrap__preview__image data-v-e6e7992a" style="{{'width:'+(item.g0)+';'+('height:'+(item.g1)+';')}}" src="{{item.$orig.thumb||item.$orig.url}}" mode="{{imageMode}}" data-event-opts="{{[['tap',[['onPreviewImage',['$0',index],[[['lists','',index]]]]]]]}}" bindtap="__e"></image></block><block wx:else><view data-event-opts="{{[['tap',[['onPreviewVideo',['$0',index],[[['lists','',index]]]]]]]}}" class="uv-upload__wrap__preview__other data-v-e6e7992a" style="{{'width:'+(item.g2)+';'+('height:'+(item.g3)+';')}}" bindtap="__e"><uv-icon vue-id="{{'c98361a6-1-'+index}}" color="#80CBF9" size="26" name="{{item.$orig.isVideo||item.$orig.type&&item.$orig.type==='video'?'movie':'folder'}}" class="data-v-e6e7992a" bind:__l="__l"></uv-icon><text class="uv-upload__wrap__preview__other__text data-v-e6e7992a">{{item.$orig.isVideo||item.$orig.type&&item.$orig.type==='video'?'视频':'文件'}}</text></view></block><block wx:if="{{item.$orig.status==='uploading'||item.$orig.status==='failed'}}"><view class="uv-upload__status data-v-e6e7992a"><view class="uv-upload__status__icon data-v-e6e7992a"><block wx:if="{{item.$orig.status==='failed'}}"><uv-icon vue-id="{{'c98361a6-2-'+index}}" name="close-circle" color="#ffffff" size="25" class="data-v-e6e7992a" bind:__l="__l"></uv-icon></block><block wx:else><uv-loading-icon vue-id="{{'c98361a6-3-'+index}}" size="22" mode="circle" class="data-v-e6e7992a" bind:__l="__l"></uv-loading-icon></block></view><block wx:if="{{item.$orig.message}}"><text class="uv-upload__status__message data-v-e6e7992a">{{item.$orig.message}}</text></block></view></block><block wx:if="{{item.$orig.status!=='uploading'&&(deletable||item.$orig.deletable)}}"><view data-event-opts="{{[['tap',[['deleteItem',[index]]]]]}}" class="uv-upload__deletable data-v-e6e7992a" catchtap="__e"><view class="uv-upload__deletable__icon data-v-e6e7992a"><uv-icon vue-id="{{'c98361a6-4-'+index}}" name="close" color="#ffffff" size="10" class="data-v-e6e7992a" bind:__l="__l"></uv-icon></view></view></block><block wx:if="{{item.$orig.status==='success'}}"><view class="uv-upload__success data-v-e6e7992a"><view class="uv-upload__success__icon data-v-e6e7992a"><uv-icon vue-id="{{'c98361a6-5-'+index}}" name="checkmark" color="#ffffff" size="12" class="data-v-e6e7992a" bind:__l="__l"></uv-icon></view></view></block></view></block></block><block wx:if="{{isInCount}}"><view data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" bindtap="__e" class="data-v-e6e7992a"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><view class="{{['uv-upload__button','data-v-e6e7992a',disabled&&'uv-upload__button--disabled']}}" style="{{'width:'+($root.g4)+';'+('height:'+($root.g5)+';')}}" hover-class="{{!disabled?'uv-upload__button--hover':''}}" hover-stay-time="150" data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" catchtap="__e"><uv-icon vue-id="c98361a6-6" name="{{uploadIcon}}" size="26" color="{{uploadIconColor}}" class="data-v-e6e7992a" bind:__l="__l"></uv-icon><block wx:if="{{uploadText}}"><text class="uv-upload__button__text data-v-e6e7992a">{{uploadText}}</text></block></view></block></view></block></view><uv-preview-video vue-id="c98361a6-7" data-ref="previewVideo" class="data-v-e6e7992a vue-ref" bind:__l="__l"></uv-preview-video></view>