<page-meta page-style="{{'overflow:'+(showFinishPopup||showSignDrawer?'hidden':'visible')}}" class="data-v-57280228"></page-meta><page-container show="{{showFinishPopup||showSignDrawer}}" data-event-opts="{{[['beforeleave',[['e0',['$event']]]]]}}" bindbeforeleave="__e" class="data-v-57280228"></page-container><view class="workbench-wrap data-v-57280228"><view class="summary-card data-v-57280228"><view class="summary-title data-v-57280228">工单概况</view><view class="summary-stats data-v-57280228"><view class="stat-item total data-v-57280228"><view class="stat-label data-v-57280228">工单总数</view><view class="stat-value data-v-57280228">{{summary.totalNum}}</view></view><view class="stat-item pending data-v-57280228"><view class="stat-label data-v-57280228">待接单</view><view class="stat-value data-v-57280228">{{summary.waitNum}}</view></view><view class="stat-item processing data-v-57280228"><view class="stat-label data-v-57280228">进行中</view><view class="stat-value data-v-57280228">{{summary.doNum}}</view></view><view class="stat-item finished data-v-57280228"><view class="stat-label data-v-57280228">已完成</view><view class="stat-value data-v-57280228">{{summary.completeNum}}</view></view></view></view><view class="order-tab-bar data-v-57280228"><block wx:for="{{tabs}}" wx:for-item="tab" wx:for-index="idx" wx:key="value"><view data-event-opts="{{[['tap',[['handleClickTab',[idx]]]]]}}" class="{{['data-v-57280228','tab-item',[(currentTab===idx)?'active':'']]}}" bindtap="__e">{{''+tab.label+''}}</view></block></view><view class="order-list data-v-57280228"><block wx:if="{{$root.g0===0}}"><view class="empty-tip data-v-57280228">暂无工单</view></block><block wx:for="{{orders}}" wx:for-item="order" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['orders','id',order.id]]]]]]]}}" class="order-card data-v-57280228" catchtap="__e"><view class="order-header data-v-57280228"><view class="order-title data-v-57280228">{{order.objectName}}</view><view style="display:flex;gap:8rpx;" class="data-v-57280228"><view class="{{['order-status','data-v-57280228','status-'+order.objectStatus]}}">{{statusMap[order.objectStatus]}}</view><block wx:if="{{order.isNeedSign==1&&order.isSign==0}}"><view class="order-status status-needSign data-v-57280228">待签到</view></block></view></view><view class="order-detail-row data-v-57280228"><view class="order-label data-v-57280228">服务类型：</view><view class="order-value data-v-57280228">{{order.serverTypeName}}</view></view><view class="order-detail-row data-v-57280228"><view class="order-label data-v-57280228">服务时间：</view><view class="order-value data-v-57280228">{{order.serviceStartTime+" - "+order.serviceEndTime}}</view></view><view class="order-detail-row data-v-57280228"><view class="order-label data-v-57280228">客户名称：</view><view class="order-value data-v-57280228">{{order.customerName}}</view></view><view class="order-detail-row data-v-57280228"><view class="order-label data-v-57280228">联系人：</view><view class="order-value data-v-57280228">{{order.contact}}</view></view><view class="order-detail-row data-v-57280228"><view class="order-label data-v-57280228">联系电话：</view><view class="order-value data-v-57280228">{{order.contactPhone}}</view><block wx:if="{{order.contactPhone}}"><u-icon style="margin-left:10rpx;cursor:pointer;" vue-id="{{'8dd740cc-1-'+__i0__}}" name="phone" size="20" color="#2979ff" data-event-opts="{{[['^tap',[['makePhoneCall',['$0'],[[['orders','id',order.id,'contactPhone']]]]]]]}}" catch:tap="__e" class="data-v-57280228" bind:__l="__l"></u-icon></block></view><view class="order-detail-row data-v-57280228"><view class="order-label data-v-57280228">地址：</view><view class="order-value data-v-57280228">{{order.distributionAddress}}</view></view><view class="order-actions data-v-57280228"><block wx:if="{{order.objectStatus==3}}"><u-button class="action-btn primary data-v-57280228" vue-id="{{'8dd740cc-2-'+__i0__}}" type="primary" plain="{{true}}" data-event-opts="{{[['^tap',[['handleAccept',['$0'],[[['orders','id',order.id]]]]]]]}}" catch:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">接单</u-button></block><block wx:if="{{order.objectStatus==1&&order.isNeedSign==1&&(order.isSign==0||order.isSign==null)}}"><u-button class="action-btn data-v-57280228" vue-id="{{'8dd740cc-3-'+__i0__}}" type="primary" icon="map" plain="{{true}}" hairline="{{true}}" data-event-opts="{{[['^tap',[['handleSign',['$0'],[[['orders','id',order.id]]]]]]]}}" catch:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">签到</u-button></block><block wx:if="{{order.isNeedSign==1?order.isSign==1:order.objectStatus==1}}"><u-button class="action-btn primary data-v-57280228" vue-id="{{'8dd740cc-4-'+__i0__}}" plain="{{true}}" type="primary" data-event-opts="{{[['^tap',[['handleFinish',['$0'],[[['orders','id',order.id]]]]]]]}}" catch:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">完成</u-button></block></view></view></block></view><u-popup style="z-index:9999;" vue-id="8dd740cc-5" show="{{showSignDrawer}}" type="bottom" closeOnClickOverlay="{{true}}" mask-click="{{true}}" background="#fff" data-event-opts="{{[['^close',[['e1']]]]}}" bind:close="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view style="padding:32rpx 24rpx;" class="data-v-57280228"><view style="font-size:32rpx;font-weight:bold;margin-bottom:24rpx;" class="data-v-57280228">签到</view><view style="margin-bottom:24rpx;" class="data-v-57280228"><view style="font-size:28rpx;margin-bottom:8rpx;" class="data-v-57280228">选择位置</view><u-button vue-id="{{('8dd740cc-6')+','+('8dd740cc-5')}}" type="primary" icon="map" loading="{{locationLoading}}" loadingText="正在获取位置..." plain="{{true}}" data-event-opts="{{[['^tap',[['chooseLocation']]]]}}" bind:tap="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}">{{''+(signAddress?signAddress:"点击获取位置")+''}}</u-button></view><view style="margin-bottom:24rpx;" class="data-v-57280228"><view style="font-size:28rpx;margin-bottom:8rpx;" class="data-v-57280228">上传照片</view><uv-upload vue-id="{{('8dd740cc-7')+','+('8dd740cc-5')}}" accept="media" fileList="{{signPhotoUrl}}" multiple="{{true}}" maxCount="{{9}}" data-event-opts="{{[['^clickPreview',[['handleClickPreview']]],['^afterRead',[['afterReadSign']]],['^delete',[['handleDeleteSign']]]]}}" bind:clickPreview="__e" bind:afterRead="__e" bind:delete="__e" class="data-v-57280228" bind:__l="__l"></uv-upload></view><u-button vue-id="{{('8dd740cc-8')+','+('8dd740cc-5')}}" type="primary" data-event-opts="{{[['^tap',[['submitSign']]]]}}" bind:tap="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}">确认签到</u-button></view></u-popup><u-modal vue-id="8dd740cc-9" show="{{acceptOrderModalShow}}" title="确认接单" content="确认接单吗？" showCancelButton="{{true}}" asyncClose="{{true}}" data-ref="uModal" data-event-opts="{{[['^confirm',[['acceptOrderConfirm']]],['^cancel',[['e2']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-57280228 vue-ref" bind:__l="__l"></u-modal><u-popup vue-id="8dd740cc-10" show="{{showFinishPopup}}" mode="bottom" closeable="{{true}}" data-event-opts="{{[['^close',[['e3']]]]}}" bind:close="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view class="finish-popup data-v-57280228"><view class="popup-title data-v-57280228">完成工单</view><view class="popup-content data-v-57280228"><u-form vue-id="{{('8dd740cc-11')+','+('8dd740cc-10')}}" labelPosition="top" labelWidth="auto" model="{{finishForm}}" data-ref="finishForm" class="data-v-57280228 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('8dd740cc-12')+','+('8dd740cc-11')}}" borderBottom="{{true}}" labelPosition="left" label="服务开始时间" required="{{true}}" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view style="display:flex;justify-content:flex-end;align-items:center;" class="data-v-57280228"><text data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" bindtap="__e" class="data-v-57280228">{{$root.m0+''}}<u-icon vue-id="{{('8dd740cc-13')+','+('8dd740cc-12')}}" label="uView" size="40" name="arrow-right" class="data-v-57280228" bind:__l="__l"></u-icon></text></view><u-datetime-picker vue-id="{{('8dd740cc-14')+','+('8dd740cc-12')}}" show="{{shiwServiceStartTimePicker}}" mode="datetime" visibleItemCount="{{5}}" value="{{finishForm.serviceStartTime}}" data-event-opts="{{[['^cancel',[['e5']]],['^confirm',[['e6']]],['^input',[['__set_model',['$0','serviceStartTime','$event',[]],['finishForm']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-57280228" bind:__l="__l"></u-datetime-picker></u-form-item><u-form-item vue-id="{{('8dd740cc-15')+','+('8dd740cc-11')}}" borderBottom="{{true}}" labelPosition="left" label="服务结束时间" required="{{true}}" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view style="display:flex;justify-content:flex-end;align-items:center;" class="data-v-57280228"><text data-event-opts="{{[['tap',[['e7',['$event']]]]]}}" bindtap="__e" class="data-v-57280228">{{$root.m1+''}}<u-icon vue-id="{{('8dd740cc-16')+','+('8dd740cc-15')}}" label="uView" size="40" name="arrow-right" class="data-v-57280228" bind:__l="__l"></u-icon></text></view><u-datetime-picker vue-id="{{('8dd740cc-17')+','+('8dd740cc-15')}}" show="{{shiwServiceEndTimePicker}}" mode="datetime" visibleItemCount="{{5}}" value="{{finishForm.serviceEndTime}}" data-event-opts="{{[['^cancel',[['e8']]],['^confirm',[['e9']]],['^input',[['__set_model',['$0','serviceEndTime','$event',[]],['finishForm']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-57280228" bind:__l="__l"></u-datetime-picker></u-form-item><u-form-item vue-id="{{('8dd740cc-18')+','+('8dd740cc-11')}}" labelPosition="left" label="实际使用工时" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('8dd740cc-19')+','+('8dd740cc-18')}}" placeholder="请输入实际使用工时" border="{{false}}" type="digit" value="{{finishForm.useTimes}}" data-event-opts="{{[['^input',[['__set_model',['$0','useTimes','$event',[]],['finishForm']]]]]}}" class="data-v-57280228" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('8dd740cc-20')+','+('8dd740cc-11')}}" labelPosition="left" label="完成情况" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><dic-picker bind:input="__e" vue-id="{{('8dd740cc-21')+','+('8dd740cc-20')}}" dicUrl="/blade-system/dict-biz/dictionary?code=completeType" placeholder="请选择完成情况" value="{{finishForm.completeStatus}}" data-event-opts="{{[['^input',[['__set_model',['$0','completeStatus','$event',[]],['finishForm']]]]]}}" class="data-v-57280228" bind:__l="__l"></dic-picker></u-form-item><u-form-item vue-id="{{('8dd740cc-22')+','+('8dd740cc-11')}}" borderBottom="{{true}}" label="现场图" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><uv-upload vue-id="{{('8dd740cc-23')+','+('8dd740cc-22')}}" accept="media" fileList="{{finishForm.workOrderPhotos}}" multiple="{{true}}" maxCount="{{9}}" data-event-opts="{{[['^clickPreview',[['handleClickPreview']]],['^afterRead',[['afterReadForXC']]],['^delete',[['handleDeleteForXC']]]]}}" bind:clickPreview="__e" bind:afterRead="__e" bind:delete="__e" class="data-v-57280228" bind:__l="__l"></uv-upload></u-form-item><u-form-item vue-id="{{('8dd740cc-24')+','+('8dd740cc-11')}}" borderBottom="{{true}}" label="处理结果图" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><uv-upload vue-id="{{('8dd740cc-25')+','+('8dd740cc-24')}}" accept="media" fileList="{{finishForm.handleResultPhotos}}" multiple="{{true}}" maxCount="{{9}}" data-event-opts="{{[['^clickPreview',[['handleClickPreview']]],['^afterRead',[['afterReadForFinish']]],['^delete',[['handleDeleteForFinish']]]]}}" bind:clickPreview="__e" bind:afterRead="__e" bind:delete="__e" class="data-v-57280228" bind:__l="__l"></uv-upload></u-form-item><u-form-item vue-id="{{('8dd740cc-26')+','+('8dd740cc-11')}}" borderBottom="{{true}}" label="服务复盘" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('8dd740cc-27')+','+('8dd740cc-26')}}" border="none" placeholder="请输入服务复盘" value="{{finishForm.serviceReorder}}" data-event-opts="{{[['^input',[['__set_model',['$0','serviceReorder','$event',[]],['finishForm']]]]]}}" class="data-v-57280228" bind:__l="__l"></u-textarea></u-form-item><u-form-item vue-id="{{('8dd740cc-28')+','+('8dd740cc-11')}}" borderBottom="{{true}}" label="备注" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('8dd740cc-29')+','+('8dd740cc-28')}}" border="none" placeholder="请输入备注信息" value="{{finishForm.completeRemark}}" data-event-opts="{{[['^input',[['__set_model',['$0','completeRemark','$event',[]],['finishForm']]]]]}}" class="data-v-57280228" bind:__l="__l"></u-textarea></u-form-item></u-form></view><view class="popup-footer data-v-57280228"><u-button vue-id="{{('8dd740cc-30')+','+('8dd740cc-10')}}" type="primary" data-event-opts="{{[['^click',[['submitFinish']]]]}}" bind:click="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}">提交</u-button></view></view></u-popup></view>