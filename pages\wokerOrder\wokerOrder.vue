<template>
  <page-meta
    :page-style="
      'overflow:' + (showFinishPopup || showSettlePopup ? 'hidden' : 'visible')
    "
  ></page-meta>
  <page-container
    :show="showFinishPopup || showSettlePopup"
    @beforeleave="
      showFinishPopup = false;
      showSettlePopup = false;
    "
  ></page-container>
  <view class="workbench-wrap">
   

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <u-search
          shape="square"
          v-model="keyWords"
          placeholder="搜索工单名称、客户名称、联系人"
          :showAction="true"
          actionText="搜索"
          @search="handleSearch"
          @custom="handleSearch"
        ></u-search>
      </view>
      <!-- 筛选按钮 -->
      <view class="filter-btn" @click="showFilterPopup = true">
        <u-icon name="list" size="20" color="#666"></u-icon>
        <text>筛选</text>
      </view>
    </view>

    <!-- 工单tab栏 -->
    <view class="order-tab-bar">
      <view
        v-for="(tab, idx) in subList"
        :key="tab.value"
        :class="['tab-item', { active: currentStatus === idx }]"
        @tap="sectionChange(idx)"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <u-popup
      :show="showFilterPopup"
      mode="bottom"
      @close="showFilterPopup = false"
      :closeable="true"
    >
      <view class="filter-popup">
        <view class="popup-title">筛选条件</view>
        <view class="filter-content">
          <view class="filter-item">
            <view class="filter-label">支付状态</view>
            <view class="filter-options">
              <view
                v-for="status in payStatus"
                :key="status.value"
                :class="['filter-option', { active: filterForm.payStatus === status.value }]"
                @click="filterForm.payStatus = filterForm.payStatus === status.value ? null : status.value"
              >
                {{ status.label }}
              </view>
            </view>
          </view>
          <view class="filter-item">
            <view class="filter-label">服务时间</view>
            <view class="date-range">
              <u-input
                v-model="filterForm.startDate"
                placeholder="开始日期"
                type="select"
                @click="showStartDatePicker = true"
              />
              <text style="margin: 0 10rpx;">至</text>
              <u-input
                v-model="filterForm.endDate"
                placeholder="结束日期"
                type="select"
                @click="showEndDatePicker = true"
              />
            </view>
          </view>
        </view>
        <view class="filter-footer">
          <u-button type="info" plain @click="resetFilter">重置</u-button>
          <u-button type="primary" @click="applyFilter">确定</u-button>
        </view>
      </view>
    </u-popup>

    <!-- 日期选择器 -->
    <u-datetime-picker
      v-model="filterForm.startDate"
      :show="showStartDatePicker"
      @cancel="showStartDatePicker = false"
      @confirm="showStartDatePicker = false"
      mode="date"
    ></u-datetime-picker>
    <u-datetime-picker
      v-model="filterForm.endDate"
      :show="showEndDatePicker"
      @cancel="showEndDatePicker = false"
      @confirm="showEndDatePicker = false"
      mode="date"
    ></u-datetime-picker>
    <!-- 工单列表 -->
    <view class="order-list">
      <view v-if="list.length === 0" class="empty-tip"> 暂无工单 </view>
      <view
        v-for="item in list"
        @click.stop="toDetail(item)"
        :key="item.id"
        class="order-card"
      >
        <view class="order-header">
          <view class="order-title">{{ item.objectName || "--" }}</view>
          <view style="display: flex; gap: 8rpx">
            <view
              class="order-status"
              :class="'status-' + item.objectStatus"
            >{{ statusMap[item.objectStatus] }}</view>
            <view
              v-if="item.payStatus !== null && item.payStatus !== undefined"
              class="order-status pay-status"
              :class="'pay-status-' + item.payStatus"
            >
              {{ payStatusMap[item.payStatus] }}
            </view>
          </view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">服务类型：</view>
          <view class="order-value">{{ item.serverTypeName || "--" }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">服务客户：</view>
          <view class="order-value">{{ item.finalCustomer || "--" }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">联系人：</view>
          <view class="order-value">{{ item.contact || "--" }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">联系电话：</view>
          <view class="order-value">{{ item.contactPhone || "--" }}</view>
          <u-icon
            v-if="item.contactPhone"
            name="phone"
            size="20"
            color="#2979ff"
            @tap.stop="makePhoneCall(item.contactPhone)"
            style="margin-left: 10rpx; cursor: pointer;"
          ></u-icon>
        </view>
        <view class="order-detail-row">
          <view class="order-label">服务地址：</view>
          <view class="order-value">{{ item.distributionAddress || "--" }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">服务时间：</view>
          <view class="order-value">{{ item.serviceStartTime || "--" }} - {{ item.serviceEndTime || "--" }}</view>
        </view>
       
        <view class="order-detail-row" v-if="item.orderPrice">
          <view class="order-label">金额：</view>
          <view class="order-value price">¥{{ item.orderPrice }}</view>
        </view>

        <!-- 操作区 -->
        <view class="order-actions">
          <template v-if="item.objectStatus == 3">
            <u-button
              type="primary"
              plain
              class="action-btn primary"
              @tap.stop="handleStart(item)"
            >接单</u-button>
          </template>
          <template v-else-if="item.objectStatus == 1">
            <u-button
              class="action-btn primary"
              plain
              type="primary"
              @tap.stop="handleFinish(item)"
            >完成</u-button>
          </template>
          <template v-else-if="item.objectStatus == 2 && item.payStatus == null">
            <u-button
              class="action-btn primary"
              plain
              type="primary"
              @tap.stop="handleSettled(item)"
            >申请结算</u-button>
          </template>
        </view>
      </view>
      <uni-load-more
        :status="list.length == page.total ? 'noMore' : 'loading'"
      ></uni-load-more>
    </view>
    <!-- 接单确认弹窗 -->
    <u-modal
      :show="modalShow"
      @confirm="confirm"
      ref="uModal"
      title="确认接单"
      content="确认接单吗？"
      showCancelButton
      @cancel="modalShow = false"
      :asyncClose="true"
    ></u-modal>

    <!-- 用户信息完善提示 -->
    <u-modal
      :show="show"
      content="你还未完善手机号,姓名等信息,请点击确认去完善"
      @confirm="toUserInfo"
    ></u-modal>

    <!-- 完成工单弹窗 -->
    <u-popup
      :show="showFinishPopup"
      mode="bottom"
      @close="showFinishPopup = false"
      :closeable="true"
    >
      <view class="finish-popup">
        <view class="popup-title">完成工单</view>
        <view class="popup-content">
          <u-form
            labelPosition="top"
            labelWidth="auto"
            :model="finishForm"
            ref="finishForm"
          >
            <u-form-item
              borderBottom
              labelPosition="left"
              label="完成时间"
              required
            >
              <view
                style="
                  display: flex;
                  justify-content: flex-end;
                  align-items: center;
                "
              >
                <text @click="showFinishTimePicker = true"
                  >{{
                    dateFormat(
                      new Date(Number(finishForm.finishTime)),
                      "yyyy-MM-dd hh:mm"
                    ) || "请选择完成时间"
                  }}
                  <u-icon label="uView" size="40" name="arrow-right"></u-icon
                ></text>
              </view>
              <u-datetime-picker
                v-model="finishForm.finishTime"
                :show="showFinishTimePicker"
                @cancel="showFinishTimePicker = false"
                @confirm="showFinishTimePicker = false"
                mode="datetime"
                :visibleItemCount="5"
              ></u-datetime-picker>
            </u-form-item>
            <u-form-item borderBottom label="备注">
              <u-textarea
                v-model="finishForm.remark"
                border="none"
                placeholder="请输入备注信息"
              ></u-textarea>
            </u-form-item>
            <u-form-item borderBottom label="上传图片/视频">
              <uv-upload
                accept="media"
                @clickPreview="handleClickPreview"
                :fileList="finishForm.fileList"
                @afterRead="afterRead"
                @delete="handleDelete"
                multiple
                :maxCount="9"
              >
              </uv-upload>
            </u-form-item>
          </u-form>
        </view>
        <view class="popup-footer">
          <u-button type="primary" @click="submitFinish">提交</u-button>
        </view>
      </view>
    </u-popup>
    <u-popup
      :show="showSettlePopup"
      mode="bottom"
      @close="showSettlePopup = false"
      :closeable="true"
    >
      <view class="finish-popup">
        <view class="popup-title">申请结算</view>
        <view class="popup-content">
          <u-form
            labelPosition="top"
            labelWidth="auto"
            :model="settleForm"
            ref="settleForm"
          >
            <!-- 结算金额 -->
            <u-form-item borderBottom label="结算金额">
              <u--input
                v-model="settleForm.totalPrice"
                placeholder="请输入结算金额"
                border="none"
                type="number"
              />
            </u-form-item>
            <u-form-item borderBottom label="备注">
              <u-textarea
                v-model="settleForm.applyContent"
                border="none"
                placeholder="请输入备注信息"
              ></u-textarea>
            </u-form-item>
          </u-form>
        </view>
        <view class="popup-footer">
          <u-button type="primary" @click="submitSettle">提交</u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>
<script>
import { dateFormat } from "../../utils/date";
import http from "../../http/api.js";
;
export default {
  name: "workerOrder",
  data() {
    return {
      keyWords: "",
      currentStatus: 0,
      dateFormat,
      list: [],
      showFinishPopup: false,
      showFinishTimePicker: false,
      modalShow: false,
      finishForm: {
        finishTime: Number(new Date()),
        remark: "",
        fileList: [],
      },
      page: {
        size: 10,
        current: 1,
        total: 0,
      },
      subList: [
        {
          label: "待接单",
          value: 3,
        },
        {
          label: "进行中",
          value: 1,
        },
        {
          label: "已完成",
          value: 2,
        },
      ],
      payStatus: [
        {
          value: 0,
          label: "待审核",
        },
        {
          value: 1,
          label: "待付款",
        },
        {
          value: 2,
          label: "已付款",
        },
        {
          value: 3,
          label: "审核失败",
        },
      ],
      show: false,
      showSettlePopup: false,
      settleForm: {},
      // 新增字段
      summary: {
        totalNum: 0,
        waitNum: 0,
        doNum: 0,
        completeNum: 0,
      },
      statusMap: {
        3: "待接单",
        1: "进行中",
        2: "已完成",
      },
      payStatusMap: {
        0: "待审核",
        1: "待付款",
        2: "已付款",
        3: "审核失败",
      },
      // 筛选相关
      showFilterPopup: false,
      showStartDatePicker: false,
      showEndDatePicker: false,
      filterForm: {
        payStatus: null,
        startDate: "",
        endDate: "",
      },
    };
  },
  onReady() {
    this.getList();
    this.getTaskStatistics();
  },
  onShow() {
    this.validatePhone();
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  onReachBottom() {
    this.scrolltolower();
  },
  methods: {
    // 获取工单统计数据
    getTaskStatistics() {
      this.$u.api.getTaskStatistics().then((data) => {
        this.summary = data.data || {};
      }).catch(() => {
        // 如果接口不存在，使用默认值
        this.summary = {
          totalNum: 0,
          waitNum: 0,
          doNum: 0,
          completeNum: 0,
        };
      });
    },
    // 刷新数据
    refreshData() {
      this.page.current = 1;
      this.list = [];
      this.getList();
      this.getTaskStatistics();
      uni.stopPullDownRefresh();
    },
    getList() {
      const params = {
        size: this.page.size,
        current: this.page.current,
        objectStatus: this.subList[this.currentStatus].value,
        objectName: this.keyWords,
      };

      // 添加筛选条件
      if (this.filterForm.payStatus !== null) {
        params.payStatus = this.filterForm.payStatus;
      }
      if (this.filterForm.startDate) {
        params.startDate = this.filterForm.startDate;
      }
      if (this.filterForm.endDate) {
        params.endDate = this.filterForm.endDate;
      }

      this.$u.api
        .getWorkerOrder(params)
        .then((res) => {
          this.list = [...this.list, ...res.data.records];
          this.page.total = res.data.total;
        });
    },
    scrolltolower() {
      console.log(1111);

      if (this.list.length == this.page.total) return;
      this.page.current++;
      this.getList();
    },
    sectionChange(index) {
      this.currentStatus = index;
      this.page.current = 1;
      this.list = [];
      this.getList();
    },
    handleSearch() {
      this.list = [];
      this.page.current = 1;
      this.getList();
    },
    handleStart(item) {
      this.currentItem = item;
      this.modalShow = true;
    },
    confirm() {
      this.$u.api.startWorkerOrder(this.currentItem.id).then((res) => {
        this.modalShow = false;
        this.page.current = 1;
        this.list = [];
        this.getList();
      });
    },
    handleFinish(item) {
      this.showFinishPopup = true;
      this.currentItem = item;
    },
    afterRead(event) {
      console.log(event);
      const { file } = event;
      const indexAll = this.finishForm.fileList.length;
      file.forEach((item, index) => {
        this.finishForm.fileList.push({
          ...item,
          status: "uploading",
          message: "上传中",
          // url: item.thumb,
          index: indexAll + index,
        });
      });
      file.forEach((item, index) => {
        this.uploadFile(item.url, indexAll + index);
      });
    },
    uploadFile(url, index) {
      return new Promise((resolve) => {
        const params = {
          filePath: url,
          name: "file",
        };
        http.upload("/blade-resource/attach/upload", params).then((res) => {
          this.finishForm.fileList.find((item) => item.index == index).status =
            "success";
          this.finishForm.fileList.find((item) => item.index == index).message =
            "";
          this.finishForm.fileList.find((item) => item.index == index).url =
            res.data.link;
          this.finishForm.fileList.find((item) => item.index == index).id =
            res.data.id;
        });
      });
    },
    handleDelete({ file, index, name }) {
      console.log(file, index, name);
      this.finishForm.fileList.splice(index, 1);
    },
    handleClickPreview(url, lists, name) {
      console.log(url, lists, name);
    },

    submitFinish() {
      if (!this.finishForm.finishTime) {
        this.$u.toast("请选择完成时间");
        return;
      }
      const formData = {
        id: this.currentItem.id,
        finishTime: this.dateFormat(
          new Date(Number(this.finishForm.finishTime)),
          "yyyy-MM-dd hh:mm:ss"
        ),
        completeRemark: this.finishForm.remark,
        completeFiles:
          this.finishForm.fileList &&
          this.finishForm.fileList.map((item) => item.id).join(","),
      };
      this.$u.api
        .finishWorkerOrder(formData)
        .then((res) => {
          this.$u.toast("提交成功");
          this.showFinishPopup = false;
          this.finishForm = {
            finishTime: "",
            remark: "",
            fileList: [],
          };
          this.getList();
        })
        .catch((err) => {
          this.$u.toast(err.message || "提交失败");
        });
    },
    toDetail(item) {
      uni.navigateTo({
        url: "/pages/wokerOrder/wokerOrderDetail?id=" + item.id,
      });
    },
    validatePhone() {
      this.$u.api.userInfo().then((res) => {
        ;

        if (!res.data.phone) {
          this.show = true;
        } else {
          console.log(res);
          this.show = false;
        }
      });
    },
    toUserInfo() {
      uni.navigateTo({
        url: "/pages/person/userInfo",
      });
    },
    handleSettled(item) {
      this.showSettlePopup = true;
      this.settleForm = {
        objectId: item.id,
        totalPrice: item.orderPrice,
      };
    },
    submitSettle() {
      this.$u.api.applySettlement(this.settleForm).then((res) => {
        this.$u.toast("申请成功");
        this.showSettlePopup = false;
        this.settleForm = {
          objectId: "",
          totalPrice: "",
        };
        this.list = [];
        this.page.current = 1;
        this.getList();
      });
    },
    // 筛选相关方法
    resetFilter() {
      this.filterForm = {
        payStatus: null,
        startDate: "",
        endDate: "",
      };
    },
    applyFilter() {
      this.showFilterPopup = false;
      this.page.current = 1;
      this.list = [];
      this.getList();
    },
    // 拨打电话
    makePhoneCall(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        success() {
          console.log("拨打电话成功！");
        },
        fail(err) {
          console.log("拨打电话失败！", err);
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.workbench-wrap {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 32rpx 0 0 0;
  box-sizing: border-box;
}

// 工单概况统计卡片
.summary-card {
  margin: 32rpx 32rpx 24rpx 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);
  padding: 32rpx 24rpx 24rpx 24rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #333;
}

.summary-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d8cf0;
}

.stat-item.total .stat-value {
  color: #2d8cf0;
}

.stat-item.pending .stat-value {
  color: #ff9900;
}

.stat-item.processing .stat-value {
  color: #19be6b;
}

.stat-item.finished .stat-value {
  color: #909399;
}

// 搜索容器
.search-container {
  display: flex;
  align-items: center;
  margin: 0 32rpx 16rpx 32rpx;
  gap: 16rpx;
}

.search-box {
  flex: 1;
}

.filter-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 68rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);

  text {
    font-size: 20rpx;
    color: #666;
    margin-top: 4rpx;
  }
}

// Tab栏
.order-tab-bar {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin: 0 32rpx 16rpx 32rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  transition: all 0.2s;
}

.tab-item.active {
  color: #2d8cf0;
  font-weight: bold;
  background: #e6f7ff;
}

// 工单列表
.order-list {
  margin: 0 32rpx;
}

.order-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.04);
  margin-bottom: 24rpx;
  padding: 24rpx 20rpx 16rpx 20rpx;
  transition: box-shadow 0.2s;
}

.order-card:hover {
  box-shadow: 0 8rpx 32rpx 0 rgba(45, 140, 240, 0.08);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.order-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.order-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  background: #f5f7fa;
}

.order-status.status-3 {
  color: #ff9900;
  background: #fff7e6;
}

.order-status.status-1 {
  color: #19be6b;
  background: #e6ffed;
}

.order-status.status-2 {
  color: #909399;
  background: #f4f4f5;
}

.pay-status {
  font-size: 22rpx;
}

.pay-status.pay-status-0 {
  color: #ff9900;
  background: #fff7e6;
}

.pay-status.pay-status-1 {
  color: #f56c6c;
  background: #fef0f0;
}

.pay-status.pay-status-2 {
  color: #19be6b;
  background: #e6ffed;
}

.pay-status.pay-status-3 {
  color: #909399;
  background: #f4f4f5;
}

.order-detail-row {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #555;
  margin-bottom: 8rpx;
}

.order-label {
  min-width: 120rpx;
  color: #888;
  font-weight: 400;
}

.order-value {
  flex: 1;
  color: #333;
  word-break: break-all;
}

.order-value.price {
  color: #f56c6c;
  font-weight: 600;
}

.order-actions {
  display: flex;
  gap: 24rpx;
  border-top: 1px solid #f0f0f0;
  margin-top: 18rpx;
  padding-top: 18rpx;
  justify-content: flex-end;
  background: #fff;
}

.action-btn {
  min-width: 120rpx;
  padding: 0 32rpx;
  height: 56rpx;
  line-height: 56rpx;
  border: none;
  border-radius: 32rpx;
  background: #f5f7fa;
  color: #2d8cf0;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0;
  outline: none;
  box-shadow: 0 2rpx 8rpx 0 rgba(45, 140, 240, 0.04);
  transition: background 0.2s, color 0.2s;
}

.action-btn.primary {
  background: linear-gradient(90deg, #2d8cf0 0%, #57a3f3 100%);
  color: #fff;
}

.action-btn:active {
  opacity: 0.85;
}

.empty-tip {
  text-align: center;
  color: #bbb;
  font-size: 28rpx;
  margin: 64rpx 0;
}

// 筛选弹窗
.filter-popup {
  padding: 30rpx;

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
  }
}

.filter-content {
  margin-bottom: 30rpx;
}

.filter-item {
  margin-bottom: 30rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option {
  padding: 12rpx 24rpx;
  background: #f5f7fa;
  border-radius: 32rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.2s;
}

.filter-option.active {
  background: #2d8cf0;
  color: #fff;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.filter-footer {
  display: flex;
  gap: 24rpx;

  .u-button {
    flex: 1;
  }
}

// 完成弹窗
.finish-popup {
  height: 80vh;
  overflow-y: auto;
  position: relative;

  .popup-title {
    padding-top: 20rpx;
    position: sticky;
    background-color: #fff;
    top: 0;
    z-index: 10;
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
  }

  .popup-content {
    padding: 30rpx;
    margin-bottom: 30rpx;
  }

  .popup-footer {
    padding: 20rpx 0;
    position: fixed;
    bottom: 0;
    left: 20rpx;
    right: 20rpx;
    width: auto;
  }
}

::v-deep input {
  text-align: right !important;
}
</style>
