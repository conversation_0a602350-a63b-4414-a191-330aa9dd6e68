(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uv-safe-bottom/components/uv-safe-bottom/uv-safe-bottom"],{"1d95":function(t,e,n){"use strict";n.r(e);var u=n("c7f3"),a=n.n(u);for(var o in u)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(o);e["default"]=a.a},"5ca3":function(t,e,n){},"60b3":function(t,e,n){"use strict";n.r(e);var u=n("db51"),a=n("1d95");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("c866");var i=n("828b"),c=Object(i["a"])(a["default"],u["b"],u["c"],!1,null,"722aa140",null,!1,u["a"],void 0);e["default"]=c.exports},c7f3:function(t,e,n){"use strict";var u=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=u(n("d32d")),o=u(n("1b07")),i={name:"uv-safe-bottom",mixins:[a.default,o.default],data:function(){return{safeAreaBottomHeight:0,isNvue:!1}},computed:{style:function(){return this.$uv.deepMerge({},this.$uv.addStyle(this.customStyle))}},mounted:function(){}};e.default=i},c866:function(t,e,n){"use strict";var u=n("5ca3"),a=n.n(u);a.a},db51:function(t,e,n){"use strict";n.d(e,"b",(function(){return u})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var u=function(){var t=this.$createElement,e=(this._self._c,this.__get_style([this.style]));this.$mp.data=Object.assign({},{$root:{s0:e}})},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uv-safe-bottom/components/uv-safe-bottom/uv-safe-bottom-create-component',
    {
        'uni_modules/uv-safe-bottom/components/uv-safe-bottom/uv-safe-bottom-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("60b3"))
        })
    },
    [['uni_modules/uv-safe-bottom/components/uv-safe-bottom/uv-safe-bottom-create-component']]
]);
