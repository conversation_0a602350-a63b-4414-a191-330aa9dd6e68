(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uv-upload/components/uv-preview-video/uv-preview-video"],{"26a0":function(n,t,e){"use strict";e.r(t);var u=e("2dab"),o=e.n(u);for(var i in u)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(i);t["default"]=o.a},"2dab":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u={props:{src:{type:String,default:""},autoplay:{type:Boolean,default:!0}},data:function(){return{videoSrc:"",show:!1}},computed:{getSec:function(){return this.src||this.videoSrc}},methods:{open:function(n){this.videoSrc=n,this.$refs.popup.open()},close:function(){this.$refs.popup.close()},change:function(n){this.show=n.show}}};t.default=u},"3df2":function(n,t,e){},"65d2":function(n,t,e){"use strict";e.r(t);var u=e("8b4f"),o=e("26a0");for(var i in o)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(i);e("e547");var r=e("828b"),c=Object(r["a"])(o["default"],u["b"],u["c"],!1,null,"5c90ac64",null,!1,u["a"],void 0);t["default"]=c.exports},"8b4f":function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){return u}));var u={uvPopup:function(){return e.e("uni_modules/uv-popup/components/uv-popup/uv-popup").then(e.bind(null,"6406"))}},o=function(){var n=this.$createElement;this._self._c},i=[]},e547:function(n,t,e){"use strict";var u=e("3df2"),o=e.n(u);o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uv-upload/components/uv-preview-video/uv-preview-video-create-component',
    {
        'uni_modules/uv-upload/components/uv-preview-video/uv-preview-video-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("65d2"))
        })
    },
    [['uni_modules/uv-upload/components/uv-preview-video/uv-preview-video-create-component']]
]);
