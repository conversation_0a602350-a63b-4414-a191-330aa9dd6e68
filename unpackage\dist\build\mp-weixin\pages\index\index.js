(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/index"],{"0478":function(e,n,t){"use strict";t.d(n,"b",(function(){return o})),t.d(n,"c",(function(){return r})),t.d(n,"a",(function(){return i}));var i={uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(null,"073f"))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-button/u-button")]).then(t.bind(null,"f8f8"))},uPopup:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-popup/u-popup")]).then(t.bind(null,"1a2c"))},uvUpload:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uv-upload/components/uv-upload/uv-upload")]).then(t.bind(null,"ddd1"))},uModal:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-modal/u-modal")]).then(t.bind(null,"b5ea"))},uForm:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-form/u-form")]).then(t.bind(null,"a7f1"))},uFormItem:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-form-item/u-form-item")]).then(t.bind(null,"1da9"))},uDatetimePicker:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker")]).then(t.bind(null,"d33a"))},uInput:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-input/u-input")]).then(t.bind(null,"4a78"))},uTextarea:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-textarea/u-textarea")]).then(t.bind(null,"6704"))}},o=function(){var e=this,n=e.$createElement,t=(e._self._c,e.orders.length),i=e.dateFormat(new Date(Number(e.finishForm.serviceStartTime)),"yyyy-MM-dd hh:mm")||"青选择服务开始时间",o=e.dateFormat(new Date(Number(e.finishForm.serviceEndTime)),"yyyy-MM-dd hh:mm")||"青选择服务结束时间";e._isMounted||(e.e0=function(n){e.showFinishPopup=!1,e.showSignDrawer=!1},e.e1=function(n){e.showSignDrawer=!1},e.e2=function(n){e.acceptOrderModalShow=!1},e.e3=function(n){e.showFinishPopup=!1},e.e4=function(n){e.shiwServiceStartTimePicker=!0},e.e5=function(n){e.shiwServiceStartTimePicker=!1},e.e6=function(n){e.shiwServiceStartTimePicker=!1},e.e7=function(n){e.shiwServiceEndTimePicker=!0},e.e8=function(n){e.shiwServiceEndTimePicker=!1},e.e9=function(n){e.shiwServiceEndTimePicker=!1}),e.$mp.data=Object.assign({},{$root:{g0:t,m0:i,m1:o}})},r=[]},2217:function(e,n,t){"use strict";var i=t("a6de"),o=t.n(i);o.a},4222:function(e,n,t){"use strict";(function(e,n){var i=t("47a9");t("ea4a");i(t("3240"));var o=i(t("c4e7"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(o.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},a6de:function(e,n,t){},c4a2:function(e,n,t){"use strict";t.r(n);var i=t("c984"),o=t.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(r);n["default"]=o.a},c4e7:function(e,n,t){"use strict";t.r(n);var i=t("0478"),o=t("c4a2");for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);t("2217");var s=t("828b"),u=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"626e3920",null,!1,i["a"],void 0);n["default"]=u.exports},c984:function(e,n,t){"use strict";(function(e,i){var o=t("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=o(t("7eb4")),s=o(t("7ca3")),u=o(t("ee10")),a=(o(t("b851")),o(t("4d20"))),c=o(t("0814")),l=t("389b");function d(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);n&&(i=i.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,i)}return t}function h(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?d(Object(t),!0).forEach((function(n){(0,s.default)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):d(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var f={data:function(){return{showPC:!1,dateFormat:l.dateFormat,summary:{totalNum:0,waitNum:0,doNum:0,completeNum:0},tabs:[{label:"进行中",value:"processing"},{label:"待接单",value:"pending"}],currentTab:0,orders:[],statusMap:{3:"待接单",1:"进行中",2:"已完成"},showSignDrawer:!1,locationLoading:!1,signAddress:null,signPhotoUrl:"",signRemark:"",signOrder:null,qqmapsdk:null,currentItem:null,acceptOrderModalShow:!1,showFinishPopup:!1,shiwServiceStartTimePicker:!1,shiwServiceEndTimePicker:!1,finishForm:{serviceStartTime:Number(new Date),serviceEndTime:Number(new Date),completeRemark:"",useTimes:null,serviceReorder:"",workOrderPhotos:[],handleResultPhotos:[]}}},components:{dicPicker:function(){t.e("components/dic-picker/dic-picker").then(function(){return resolve(t("7ed6"))}.bind(null,t)).catch(t.oe)}},computed:{filteredOrders:function(){var e=this.tabs[this.currentTab].value;return this.orders.filter((function(n){return n.status===e}))}},onLoad:function(){var e=this;return(0,u.default)(r.default.mark((function n(){return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,e.wxlogin();case 2:e.fetchOrders(),e.getTaskStatistics(),e.qqmapsdk=new a.default({key:"V37BZ-5JF63-HBZ3S-34XAJ-KPG2Q-74FPM"});case 5:case"end":return n.stop()}}),n)})))()},onReachBottom:function(){console.log("上拉加载"),this.fetchOrders()},onPullDownRefresh:function(){this.fetchOrders(),this.getTaskStatistics(),e.stopPullDownRefresh()},methods:{wxlogin:function(){var n=this;return new Promise((function(t){e.login({complete:function(e){n.$u.api.wxToken({code:e.code}).then((function(e){n.$u.func.login(e),t()})).catch((function(e){n.$u.func.showToast({title:e})}))}})}))},handleClickTab:function(e){this.currentTab=e,this.fetchOrders()},fetchOrders:function(){var e=this;return(0,u.default)(r.default.mark((function n(){var t;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:t={0:1,1:3},e.$u.api.getWorkerOrder({pageNum:1,pageSize:1e3,objectStatus:t[e.currentTab]}).then((function(n){e.orders=n.data.records||[]}));case 2:case"end":return n.stop()}}),n)})))()},getTaskStatistics:function(){var e=this;this.$u.api.getTaskStatistics().then((function(n){e.summary=n.data||{}}))},handleAccept:function(e){this.currentItem=e,this.acceptOrderModalShow=!0},acceptOrderConfirm:function(){var e=this;this.$u.api.startWorkerOrder(this.currentItem.id).then((function(n){e.acceptOrderModalShow=!1,e.fetchOrders()}))},handleSign:function(e){this.signOrder=e,this.showSignDrawer=!0,this.signAddress=null,this.signPhotoUrl=[],this.signRemark="",this.chooseLocation()},chooseLocation:function(){var n=this;this.locationLoading=!0,i.getLocation({type:"gcj02",success:function(t){console.log(t),n.qqmapsdk.reverseGeocoder({location:{latitude:t.latitude,longitude:t.longitude},success:function(e){console.log("逆地址解析结果：",e);var t=e.result.address_component,i=e.result.address;console.log("所在城市：",t.city),console.log("完整地址：",i),n.locationLoading=!1,n.signAddress=i},fail:function(n){this.locationLoading=!1,console.error("逆地址解析失败：",n),e.showToast({title:"位置解析失败",icon:"none"})}})},fail:function(t){console.log(t),n.locationLoading=!1,e.showToast({title:"位置选择失败",icon:"none"})}})},afterReadSign:function(e){var n=this;console.log(e);var t=e.file,i=this.signPhotoUrl.length;t.forEach((function(e,t){n.signPhotoUrl.push(h(h({},e),{},{status:"uploading",message:"上传中",index:i+t}))})),t.forEach((function(e,t){n.uploadFileSign(e.url,i+t)}))},uploadFileSign:function(e,n){var t=this;return new Promise((function(i){var o={filePath:e,name:"file"};c.default.upload("/blade-resource/attach/upload",o).then((function(e){t.signPhotoUrl.find((function(e){return e.index==n})).status="success",t.signPhotoUrl.find((function(e){return e.index==n})).message="",t.signPhotoUrl.find((function(e){return e.index==n})).url=e.data.link}))}))},handleDeleteSign:function(e){var n=e.file,t=e.index,i=e.name;console.log(n,t,i),this.signPhotoUrl.splice(t,1)},handleClickPreview:function(e,n,t){console.log(e,n,t)},submitSign:function(){var n=this;if(this.signPhotoUrl){var t={id:this.signOrder.id,address:this.signAddress,signPhotoUrl:this.signPhotoUrl.map((function(e){return e.url})).join(",")};this.$u.api.signIn(t).then((function(e){console.log(e)})).then((function(){e.showToast({title:"签到成功",icon:"success"}),n.showSignDrawer=!1,n.fetchOrders()}))}else e.showToast({title:"请上传照片",icon:"none"})},handleFinish:function(e){this.showFinishPopup=!0,this.showPC=!0,this.currentItem=e,this.finishForm.serviceStartTime=Number(new Date(e.serviceStartTime)),this.finishForm.serviceEndTime=Number(new Date(e.serviceEndTime))},afterReadForXC:function(e){var n=this;console.log(e);var t=e.file,i=this.finishForm.workOrderPhotos.length;t.forEach((function(e,t){n.finishForm.workOrderPhotos.push(h(h({},e),{},{status:"uploading",message:"上传中",index:i+t}))})),t.forEach((function(e,t){n.uploadFileForXC(e.url,i+t)}))},uploadFileForXC:function(e,n){var t=this;return new Promise((function(i){var o={filePath:e,name:"file"};c.default.upload("/blade-resource/attach/upload",o).then((function(e){t.finishForm.workOrderPhotos.find((function(e){return e.index==n})).status="success",t.finishForm.workOrderPhotos.find((function(e){return e.index==n})).message="",t.finishForm.workOrderPhotos.find((function(e){return e.index==n})).url=e.data.link,t.finishForm.workOrderPhotos.find((function(e){return e.index==n})).id=e.data.id}))}))},handleDeleteForXC:function(e){var n=e.file,t=e.index,i=e.name;console.log(n,t,i),this.finishForm.workOrderPhotos.splice(t,1)},afterReadForFinish:function(e){var n=this;console.log(e);var t=e.file,i=this.finishForm.handleResultPhotos.length;t.forEach((function(e,t){n.finishForm.handleResultPhotos.push(h(h({},e),{},{status:"uploading",message:"上传中",index:i+t}))})),t.forEach((function(e,t){n.uploadFileForFinish(e.url,i+t)}))},uploadFileForFinish:function(e,n){var t=this;return new Promise((function(i){var o={filePath:e,name:"file"};c.default.upload("/blade-resource/attach/upload",o).then((function(e){t.finishForm.handleResultPhotos.find((function(e){return e.index==n})).status="success",t.finishForm.handleResultPhotos.find((function(e){return e.index==n})).message="",t.finishForm.handleResultPhotos.find((function(e){return e.index==n})).url=e.data.link,t.finishForm.handleResultPhotos.find((function(e){return e.index==n})).id=e.data.id}))}))},handleDeleteForFinish:function(e){var n=e.file,t=e.index,i=e.name;console.log(n,t,i),this.finishForm.handleResultPhotos.splice(t,1)},submitFinish:function(){var e=this;if(this.finishForm.serviceStartTime&&this.finishForm.serviceEndTime){var n={id:this.currentItem.id,serviceStartTime:this.dateFormat(new Date(Number(this.finishForm.serviceStartTime)),"yyyy-MM-dd hh:mm:ss"),serviceEndTime:this.dateFormat(new Date(Number(this.finishForm.serviceEndTime)),"yyyy-MM-dd hh:mm:ss"),completeRemark:this.finishForm.completeRemark,handleResultPhotos:this.finishForm.handleResultPhotos&&this.finishForm.handleResultPhotos.map((function(e){return e.id})).join(","),workOrderPhotos:this.finishForm.workOrderPhotos&&this.finishForm.workOrderPhotos.map((function(e){return e.id})).join(","),useTimes:this.finishForm.useTimes,completeStatus:this.finishForm.completeStatus,serviceReorder:this.finishForm.serviceReorder};this.$u.api.finishWorkerOrder(n).then((function(n){e.$u.toast("提交成功"),e.showFinishPopup=!1,e.finishForm={finishTime:"",completeRemark:"",useTimes:null,serviceReorder:"",workOrderPhotos:[],handleResultPhotos:[]},e.fetchOrders()})).catch((function(n){e.$u.toast(n.message||"提交失败")}))}else this.$u.toast("开始时间和结束时间不能为空")},toDetail:function(n){e.navigateTo({url:"/pages/wokerOrder/wokerOrderDetail?id="+n.id})},makePhoneCall:function(n){e.makePhoneCall({phoneNumber:n,success:function(){console.log("拨打电话成功！")},fail:function(e){console.log("拨打电话失败！",e)}})}}};n.default=f}).call(this,t("df3c")["default"],t("3223")["default"])}},[["4222","common/runtime","common/vendor"]]]);