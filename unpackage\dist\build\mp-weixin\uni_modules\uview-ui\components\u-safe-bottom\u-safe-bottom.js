(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom"],{"1f60":function(t,e,n){"use strict";n.r(e);var u=n("f33f"),i=n("6eb8");for(var f in i)["default"].indexOf(f)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(f);n("ae16");var a=n("828b"),o=Object(a["a"])(i["default"],u["b"],u["c"],!1,null,"01127184",null,!1,u["a"],void 0);e["default"]=o.exports},"6eb8":function(t,e,n){"use strict";n.r(e);var u=n("df48"),i=n.n(u);for(var f in u)["default"].indexOf(f)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(f);e["default"]=i.a},ae16:function(t,e,n){"use strict";var u=n("bf25"),i=n.n(u);i.a},bf25:function(t,e,n){},df48:function(t,e,n){"use strict";(function(t){var u=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=u(n("18ce")),f={name:"u-safe-bottom",mixins:[t.$u.mpMixin,t.$u.mixin,i.default],data:function(){return{safeAreaBottomHeight:0,isNvue:!1}},computed:{style:function(){return t.$u.deepMerge({},t.$u.addStyle(this.customStyle))}},mounted:function(){}};e.default=f}).call(this,n("df3c")["default"])},f33f:function(t,e,n){"use strict";n.d(e,"b",(function(){return u})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var u=function(){var t=this.$createElement,e=(this._self._c,this.__get_style([this.style]));this.$mp.data=Object.assign({},{$root:{s0:e}})},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom-create-component',
    {
        'uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("1f60"))
        })
    },
    [['uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom-create-component']]
]);
