(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/login/login"],{"0763":function(n,t,o){"use strict";o.r(t);var e=o("c7c0"),i=o.n(e);for(var u in e)["default"].indexOf(u)<0&&function(n){o.d(t,n,(function(){return e[n]}))}(u);t["default"]=i.a},"3bdc":function(n,t,o){"use strict";(function(n,t){var e=o("47a9");o("ea4a");e(o("3240"));var i=e(o("449c"));n.__webpack_require_UNI_MP_PLUGIN__=o,t(i.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},"449c":function(n,t,o){"use strict";o.r(t);var e=o("c7ef"),i=o("0763");for(var u in i)["default"].indexOf(u)<0&&function(n){o.d(t,n,(function(){return i[n]}))}(u);o("b05d");var c=o("828b"),r=Object(c["a"])(i["default"],e["b"],e["c"],!1,null,"54e106d6",null,!1,e["a"],void 0);t["default"]=r.exports},"91a9":function(n,t,o){},b05d:function(n,t,o){"use strict";var e=o("91a9"),i=o.n(e);i.a},c7c0:function(n,t,o){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={data:function(){return{form:{phone:""},isLoading:!1,isInputFocused:!1,rules:{phone:[{required:!0,message:"请输入手机号",trigger:["blur","change"]},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:["blur","change"]}]},inputStyle:{fontSize:"16px",padding:"16px 0",backgroundColor:"#f8f9fa",borderRadius:"8px"},primaryButtonStyle:{background:"#667eea",borderRadius:"8px",height:"48px",fontSize:"16px"},wechatButtonStyle:{borderRadius:"8px",height:"48px",fontSize:"16px"}}},computed:{isValidPhone:function(){return/^1[3-9]\d{9}$/.test(this.form.phone)},canLogin:function(){return this.form.phone&&this.isValidPhone&&!this.isLoading},loginButtonText:function(){return this.isLoading?"登录中...":this.form.phone?this.isValidPhone?"手机号登录":"手机号格式错误":"请输入手机号"}},methods:{handlePhoneLogin:function(){var n=this;this.canLogin&&this.$refs.uForm.validate().then((function(t){t&&(n.isLoading=!0,n.wxlogin())})).catch((function(n){console.log("表单验证失败：",n)}))},handleGetuserinfo:function(n){console.log("微信用户信息：",n),this.isLoading=!0,this.wxlogin()},getPhoneNumber:function(t){var o=this;console.log("获取手机号：",t);var e=t.detail.code;e?(n.showLoading({title:"获取中..."}),this.$u.api.userPhone(e).then((function(t){n.hideLoading(),o.form.phone=t.data.phoneNumber,o.$u.func.showToast({title:"手机号获取成功"})})).catch((function(){n.hideLoading(),o.$u.func.showToast({title:"获取手机号失败"})}))):this.$u.func.showToast({title:"用户取消授权"})},onInputFocus:function(){this.isInputFocused=!0},onInputBlur:function(){this.isInputFocused=!1},wxlogin:function(){var t=this;n.login({complete:function(n){t.$u.api.wxToken({code:n.code,phone:t.form.phone}).then((function(n){t.$u.func.login(n),t.isLoading=!1,t.$u.func.showToast({title:"登录成功",success:function(){setTimeout((function(){t.$u.func.redirect("/pages/index/index")}),1e3)}})})).catch((function(n){t.isLoading=!1,t.$u.func.showToast({title:n||"登录失败"})}))}})}}};t.default=o}).call(this,o("df3c")["default"])},c7ef:function(n,t,o){"use strict";o.d(t,"b",(function(){return i})),o.d(t,"c",(function(){return u})),o.d(t,"a",(function(){return e}));var e={uForm:function(){return Promise.all([o.e("common/vendor"),o.e("uni_modules/uview-ui/components/u-form/u-form")]).then(o.bind(null,"a7f1"))},uFormItem:function(){return Promise.all([o.e("common/vendor"),o.e("uni_modules/uview-ui/components/u-form-item/u-form-item")]).then(o.bind(null,"1da9"))},uInput:function(){return Promise.all([o.e("common/vendor"),o.e("uni_modules/uview-ui/components/u-input/u-input")]).then(o.bind(null,"4a78"))},uButton:function(){return Promise.all([o.e("common/vendor"),o.e("uni_modules/uview-ui/components/u-button/u-button")]).then(o.bind(null,"f8f8"))}},i=function(){var n=this.$createElement;this._self._c},u=[]}},[["3bdc","common/runtime","common/vendor"]]]);