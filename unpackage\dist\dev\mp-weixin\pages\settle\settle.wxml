<page-meta page-style="{{'overflow:'+(showDetailPopup?'hidden':'visible')}}" class="data-v-362ca366"></page-meta><page-container show="{{showDetailPopup}}" data-event-opts="{{[['beforeleave',[['e0',['$event']]]]]}}" bindbeforeleave="__e" class="data-v-362ca366"></page-container><view class="workbench-wrap data-v-362ca366"><view class="summary-card data-v-362ca366"><view class="summary-title data-v-362ca366">结算概况</view><view class="summary-stats data-v-362ca366"><view class="stat-item pending data-v-362ca366"><view class="stat-label data-v-362ca366">待结算</view><view class="stat-value data-v-362ca366">{{"¥"+summary.waiSettlementPrice}}</view></view><view class="stat-item processing data-v-362ca366"><view class="stat-label data-v-362ca366">结算中</view><view class="stat-value data-v-362ca366">{{"¥"+summary.settlementPrice}}</view></view><view class="stat-item finished data-v-362ca366"><view class="stat-label data-v-362ca366">已结算</view><view class="stat-value data-v-362ca366">{{"¥"+summary.paymentPrice}}</view></view></view></view><view class="search-container data-v-362ca366"><view class="search-box data-v-362ca366"><u-search vue-id="e12f6b40-1" shape="square" placeholder="搜索工单名称、客户名称、联系人" showAction="{{true}}" actionText="搜索" value="{{keyWords}}" data-event-opts="{{[['^search',[['handleSearch']]],['^custom',[['handleSearch']]],['^input',[['__set_model',['','keyWords','$event',[]]]]]]}}" bind:search="__e" bind:custom="__e" bind:input="__e" class="data-v-362ca366" bind:__l="__l"></u-search></view></view><view class="order-tab-bar data-v-362ca366"><block wx:for="{{tabList}}" wx:for-item="tab" wx:for-index="idx" wx:key="value"><view data-event-opts="{{[['tap',[['handleTabChange',[idx]]]]]}}" class="{{['data-v-362ca366','tab-item',[(currentTab===idx)?'active':'']]}}" bindtap="__e">{{''+tab.label+''}}</view></block></view><u-popup vue-id="e12f6b40-2" show="{{showFilterPopup}}" mode="bottom" closeable="{{true}}" data-event-opts="{{[['^close',[['e1']]]]}}" bind:close="__e" class="data-v-362ca366" bind:__l="__l" vue-slots="{{['default']}}"><view class="filter-popup data-v-362ca366"><view class="popup-title data-v-362ca366">筛选条件</view><view class="filter-content data-v-362ca366"><view class="filter-item data-v-362ca366"><view class="filter-label data-v-362ca366">金额范围</view><view class="amount-range data-v-362ca366"><u-input bind:input="__e" vue-id="{{('e12f6b40-3')+','+('e12f6b40-2')}}" placeholder="最小金额" type="number" value="{{filterForm.minAmount}}" data-event-opts="{{[['^input',[['__set_model',['$0','minAmount','$event',[]],['filterForm']]]]]}}" class="data-v-362ca366" bind:__l="__l"></u-input><text style="margin:0 10rpx;" class="data-v-362ca366">至</text><u-input bind:input="__e" vue-id="{{('e12f6b40-4')+','+('e12f6b40-2')}}" placeholder="最大金额" type="number" value="{{filterForm.maxAmount}}" data-event-opts="{{[['^input',[['__set_model',['$0','maxAmount','$event',[]],['filterForm']]]]]}}" class="data-v-362ca366" bind:__l="__l"></u-input></view></view><view class="filter-item data-v-362ca366"><view class="filter-label data-v-362ca366">结算时间</view><view class="date-range data-v-362ca366"><u-input vue-id="{{('e12f6b40-5')+','+('e12f6b40-2')}}" placeholder="开始日期" type="select" value="{{filterForm.startDate}}" data-event-opts="{{[['^click',[['e2']]],['^input',[['__set_model',['$0','startDate','$event',[]],['filterForm']]]]]}}" bind:click="__e" bind:input="__e" class="data-v-362ca366" bind:__l="__l"></u-input><text style="margin:0 10rpx;" class="data-v-362ca366">至</text><u-input vue-id="{{('e12f6b40-6')+','+('e12f6b40-2')}}" placeholder="结束日期" type="select" value="{{filterForm.endDate}}" data-event-opts="{{[['^click',[['e3']]],['^input',[['__set_model',['$0','endDate','$event',[]],['filterForm']]]]]}}" bind:click="__e" bind:input="__e" class="data-v-362ca366" bind:__l="__l"></u-input></view></view></view><view class="filter-footer data-v-362ca366"><u-button vue-id="{{('e12f6b40-7')+','+('e12f6b40-2')}}" type="info" plain="{{true}}" data-event-opts="{{[['^click',[['resetFilter']]]]}}" bind:click="__e" class="data-v-362ca366" bind:__l="__l" vue-slots="{{['default']}}">重置</u-button><u-button vue-id="{{('e12f6b40-8')+','+('e12f6b40-2')}}" type="primary" data-event-opts="{{[['^click',[['applyFilter']]]]}}" bind:click="__e" class="data-v-362ca366" bind:__l="__l" vue-slots="{{['default']}}">确定</u-button></view></view></u-popup><u-datetime-picker vue-id="e12f6b40-9" show="{{showStartDatePicker}}" mode="date" value="{{filterForm.startDate}}" data-event-opts="{{[['^cancel',[['e4']]],['^confirm',[['e5']]],['^input',[['__set_model',['$0','startDate','$event',[]],['filterForm']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-362ca366" bind:__l="__l"></u-datetime-picker><u-datetime-picker vue-id="e12f6b40-10" show="{{showEndDatePicker}}" mode="date" value="{{filterForm.endDate}}" data-event-opts="{{[['^cancel',[['e6']]],['^confirm',[['e7']]],['^input',[['__set_model',['$0','endDate','$event',[]],['filterForm']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-362ca366" bind:__l="__l"></u-datetime-picker><view class="order-list data-v-362ca366"><block wx:if="{{$root.g0===0}}"><view class="empty-tip data-v-362ca366">暂无结算工单</view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['showOrderDetail',['$0'],[[['list','id',item.$orig.id]]]]]]]}}" class="order-card data-v-362ca366" catchtap="__e"><view class="order-header data-v-362ca366"><view class="order-title data-v-362ca366">{{item.$orig.objectName||"--"}}</view><view style="display:flex;gap:8rpx;" class="data-v-362ca366"><view class="{{['order-status','data-v-362ca366','status-'+item.$orig.objectStatus]}}">{{statusMap[item.$orig.objectStatus]}}</view><view class="{{['settle-status','data-v-362ca366','settle-status-'+item.m0]}}">{{''+item.m1+''}}</view></view></view><view class="order-detail-row data-v-362ca366"><view class="order-label data-v-362ca366">工单金额：</view><view class="order-value price data-v-362ca366">{{"¥"+(item.$orig.totalPrice||0)}}</view></view><view class="order-detail-row data-v-362ca366"><view class="order-label data-v-362ca366">申请时间：</view><view class="order-value data-v-362ca366">{{item.$orig.createTime}}</view></view><view class="order-detail-row data-v-362ca366"><view class="order-label data-v-362ca366">申请备注：</view><view class="order-value data-v-362ca366">{{item.$orig.applyContent||"--"}}</view></view><block wx:if="{{item.$orig.payStatus===3}}"><view class="order-actions data-v-362ca366"><u-button class="action-btn primary data-v-362ca366" vue-id="{{'e12f6b40-11-'+__i0__}}" type="primary" plain="{{true}}" data-event-opts="{{[['^tap',[['handleApplySettle',['$0'],[[['list','id',item.$orig.id]]]]]]]}}" catch:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">重新申请</u-button></view></block></view></block><uni-load-more vue-id="e12f6b40-12" status="{{$root.g1==page.total?'noMore':'loading'}}" class="data-v-362ca366" bind:__l="__l"></uni-load-more></view><u-popup vue-id="e12f6b40-13" show="{{showDetailPopup}}" mode="bottom" closeable="{{true}}" data-event-opts="{{[['^close',[['e8']]]]}}" bind:close="__e" class="data-v-362ca366" bind:__l="__l" vue-slots="{{['default']}}"><view class="detail-popup data-v-362ca366"><view class="popup-title data-v-362ca366">工单详情</view><block wx:if="{{currentItem}}"><view class="detail-content data-v-362ca366"><view class="detail-section data-v-362ca366"><view class="section-title data-v-362ca366">申请信息</view><view class="detail-item data-v-362ca366"><view class="detail-label data-v-362ca366">工单名称：</view><view class="detail-value data-v-362ca366">{{currentItem.objectName}}</view></view><view class="detail-item data-v-362ca366"><view class="detail-label data-v-362ca366">申请结算金额：</view><view class="detail-value price data-v-362ca366">{{"¥"+(currentItem.totalPrice||currentItem.orderPrice)}}</view></view><block wx:if="{{currentItem.createTime}}"><view class="detail-item data-v-362ca366"><view class="detail-label data-v-362ca366">申请时间：</view><view class="detail-value data-v-362ca366">{{currentItem.createTime}}</view></view></block><block wx:if="{{currentItem.applyRemark}}"><view class="detail-item data-v-362ca366"><view class="detail-label data-v-362ca366">备注：</view><view class="detail-value data-v-362ca366">{{currentItem.applyRemark}}</view></view></block></view><block wx:if="{{currentItem.payStatus!==null&&currentItem.payStatus!==undefined}}"><view class="detail-section data-v-362ca366"><view class="section-title data-v-362ca366">审核信息</view><view class="detail-item data-v-362ca366"><view class="detail-label data-v-362ca366">审核状态：</view><view class="{{['detail-value','data-v-362ca366','status-'+$root.m2]}}">{{''+$root.m3+''}}</view></view><block wx:if="{{currentItem.auditRemark}}"><view class="detail-item data-v-362ca366"><view class="detail-label data-v-362ca366">审核备注：</view><view class="{{['detail-value','data-v-362ca366',($root.m4==='rejected')?'reject-reason':'']}}">{{''+currentItem.auditRemark+''}}</view></view></block><block wx:if="{{currentItem.auditTime}}"><view class="detail-item data-v-362ca366"><view class="detail-label data-v-362ca366">审核时间：</view><view class="detail-value data-v-362ca366">{{currentItem.auditTime}}</view></view></block></view></block><block wx:if="{{currentItem.payStatus===2}}"><view class="detail-section data-v-362ca366"><view class="section-title data-v-362ca366">付款信息</view><block wx:if="{{currentItem.payTime}}"><view class="detail-item data-v-362ca366"><view class="detail-label data-v-362ca366">付款时间：</view><view class="detail-value data-v-362ca366">{{currentItem.payTime}}</view></view></block><block wx:if="{{currentItem.payAmount}}"><view class="detail-item data-v-362ca366"><view class="detail-label data-v-362ca366">付款金额：</view><view class="detail-value settle-amount data-v-362ca366">{{"¥"+currentItem.payAmount}}</view></view></block><block wx:if="{{$root.g2}}"><view class="detail-item data-v-362ca366"><view class="detail-label data-v-362ca366">付款照片：</view><view class="detail-value data-v-362ca366"><view class="photo-list data-v-362ca366"><block wx:for="{{currentItem.payPhotos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><image class="pay-photo data-v-362ca366" src="{{photo}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',['$0','$1'],[[['currentItem.payPhotos','',index]],'currentItem.payPhotos']]]]]}}" bindtap="__e"></image></block></view></view></view></block></view></block></view></block></view></u-popup><u-popup vue-id="e12f6b40-14" show="{{showSettlePopup}}" mode="bottom" closeable="{{true}}" data-event-opts="{{[['^close',[['e9']]]]}}" bind:close="__e" class="data-v-362ca366" bind:__l="__l" vue-slots="{{['default']}}"><view class="settle-popup data-v-362ca366"><view class="popup-title data-v-362ca366">申请结算</view><view class="popup-content data-v-362ca366"><u-form vue-id="{{('e12f6b40-15')+','+('e12f6b40-14')}}" labelPosition="top" labelWidth="auto" model="{{settleForm}}" data-ref="settleForm" class="data-v-362ca366 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('e12f6b40-16')+','+('e12f6b40-15')}}" borderBottom="{{true}}" labelPosition="left" label="结算金额" class="data-v-362ca366" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('e12f6b40-17')+','+('e12f6b40-16')}}" placeholder="请输入结算金额" border="none" type="number" value="{{settleForm.totalPrice}}" data-event-opts="{{[['^input',[['__set_model',['$0','totalPrice','$event',[]],['settleForm']]]]]}}" class="data-v-362ca366" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('e12f6b40-18')+','+('e12f6b40-15')}}" borderBottom="{{true}}" label="备注" class="data-v-362ca366" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('e12f6b40-19')+','+('e12f6b40-18')}}" border="none" placeholder="请输入备注信息" value="{{settleForm.applyContent}}" data-event-opts="{{[['^input',[['__set_model',['$0','applyContent','$event',[]],['settleForm']]]]]}}" class="data-v-362ca366" bind:__l="__l"></u-textarea></u-form-item></u-form></view><view class="popup-footer data-v-362ca366"><u-button vue-id="{{('e12f6b40-20')+','+('e12f6b40-14')}}" type="primary" data-event-opts="{{[['^click',[['submitSettle']]]]}}" bind:click="__e" class="data-v-362ca366" bind:__l="__l" vue-slots="{{['default']}}">提交申请</u-button></view></view></u-popup></view>