(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/person/userInfo"],{"47ab":function(e,n,t){"use strict";(function(e){var o=t("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=o(t("7ca3")),r=o(t("0814"));function a(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,o)}return t}function i(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?a(Object(t),!0).forEach((function(n){(0,u.default)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var c={data:function(){return{userInfo:{avatar:"",name:"",phone:""}}},onLoad:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.$u.api.userInfo().then((function(n){e.userInfo=n.data}))},onChooseAvatar:function(e){this.userInfo.avatar=e.detail.avatarUrl},getphonenumber:function(e){var n=this;console.log(e);var t=e.detail.code;t&&this.$u.api.userPhone(t).then((function(e){n.userInfo.phone=e.data.phoneNumber}))},handleSave:function(){var n=this;console.log(1111);var t=i({},this.userInfo);if(this.userInfo.avatar&&this.userInfo.avatar.indexOf("http")<0){var o={filePath:this.userInfo.avatar,name:"file"};r.default.upload("/blade-resource/attach/upload?fileName=",o).then((function(o){console.log(o),t=i(i({},n.userInfo),{},{avatar:o.data.link}),n.$u.api.updateUser(t).then((function(n){e.showToast({title:"操作成功",success:function(){setTimeout((function(){e.navigateBack()}),1e3)}})}))}))}else this.$u.api.updateUser(t).then((function(n){e.showToast({title:"操作成功",success:function(){setTimeout((function(){e.navigateBack()}),1e3)}})}))}}};n.default=c}).call(this,t("df3c")["default"])},"4cd1":function(e,n,t){"use strict";(function(e,n){var o=t("47a9");t("ea4a");o(t("3240"));var u=o(t("5cc3"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(u.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},"5cc3":function(e,n,t){"use strict";t.r(n);var o=t("6c4a"),u=t("748c");for(var r in u)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return u[e]}))}(r);t("d8ce");var a=t("828b"),i=Object(a["a"])(u["default"],o["b"],o["c"],!1,null,"609e86ea",null,!1,o["a"],void 0);n["default"]=i.exports},"6c4a":function(e,n,t){"use strict";t.d(n,"b",(function(){return u})),t.d(n,"c",(function(){return r})),t.d(n,"a",(function(){return o}));var o={uCellGroup:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-cell-group/u-cell-group")]).then(t.bind(null,"7efe"))},uCell:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-cell/u-cell")]).then(t.bind(null,"403a"))},uInput:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-input/u-input")]).then(t.bind(null,"4a78"))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-button/u-button")]).then(t.bind(null,"f8f8"))}},u=function(){var e=this.$createElement;this._self._c},r=[]},"748c":function(e,n,t){"use strict";t.r(n);var o=t("47ab"),u=t.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);n["default"]=u.a},d8ce:function(e,n,t){"use strict";var o=t("fffc"),u=t.n(o);u.a},fffc:function(e,n,t){}},[["4cd1","common/runtime","common/vendor"]]]);