(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar"],{"2da5":function(t,n,u){"use strict";u.d(n,"b",(function(){return e})),u.d(n,"c",(function(){return a})),u.d(n,"a",(function(){}));var e=function(){var t=this.$createElement,n=(this._self._c,this.__get_style([this.style]));this.$mp.data=Object.assign({},{$root:{s0:n}})},a=[]},"2df7":function(t,n,u){"use strict";var e=u("50f5"),a=u.n(e);a.a},"2e1f":function(t,n,u){"use strict";u.r(n);var e=u("2da5"),a=u("7075");for(var s in a)["default"].indexOf(s)<0&&function(t){u.d(n,t,(function(){return a[t]}))}(s);u("2df7");var i=u("828b"),r=Object(i["a"])(a["default"],e["b"],e["c"],!1,null,"c36c36f4",null,!1,e["a"],void 0);n["default"]=r.exports},"50f5":function(t,n,u){},7075:function(t,n,u){"use strict";u.r(n);var e=u("8133"),a=u.n(e);for(var s in e)["default"].indexOf(s)<0&&function(t){u.d(n,t,(function(){return e[t]}))}(s);n["default"]=a.a},8133:function(t,n,u){"use strict";var e=u("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=e(u("d32d")),s=e(u("1b07")),i=e(u("5c0f")),r={name:"uv-status-bar",mixins:[a.default,s.default,i.default],data:function(){return{}},computed:{style:function(){var t={};return t.height=this.$uv.addUnit(this.$uv.sys().statusBarHeight,"px"),this.bgColor&&(this.bgColor.indexOf("gradient")>-1?t.backgroundImage=this.bgColor:t.background=this.bgColor),this.$uv.deepMerge(t,this.$uv.addStyle(this.customStyle))}}};n.default=r}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar-create-component',
    {
        'uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("2e1f"))
        })
    },
    [['uni_modules/uv-status-bar/components/uv-status-bar/uv-status-bar-create-component']]
]);
