/**
 * 全局变量配置
 */
module.exports = {

	// 版本号
	version: '2.0.0',
	version: '2.0.1',
	// 开发环境接口Url
	// #ifdef H5
	devUrl: '',
	// #endif
	// #ifndef H5
	// devUrl: 'http://10.100.105.54:180',
	// devUrl: 'http://10.100.106.100:180',
	devUrl: 'http://oa-cs.sysvt.cn/api',
	// #endif
	// 线上环境接口Url
	// prodUrl: 'https://oa.sysvt.cn/api',
	prodUrl: 'http://oa-cs.sysvt.cn/api',
	// 后端数据的接收方式application/json;charset=UTF-8或者application/x-www-form-urlencoded;charset=UTF-8
	contentType: 'application/json;charset=UTF-8',
	// 后端返回状态码
	codeName: 'code',
	// 操作正常code
	successCode: 200,
	// 登录失效code
	invalidCode: 401,
	// 客户端ID
	clientId: 'work_order_mini',
	// 客户端密钥
	clientSecret: 'work_order_mini_secret',
	// token过期时间
	tokenTime: 3000,

}