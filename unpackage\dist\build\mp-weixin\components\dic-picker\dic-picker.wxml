<view class="data-v-15580b72"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" style="display:flex;justify-content:space-between;align-items:center;" bindtap="__e" class="data-v-15580b72"><view style="text-align:right;flex:1;" class="data-v-15580b72">{{''+(text||placeholder)+''}}</view><u-icon vue-id="c85f4c3c-1" slot="right" name="arrow-right" class="data-v-15580b72" bind:__l="__l"></u-icon></view><u-picker vue-id="c85f4c3c-2" closeOnClickOverlay="{{true}}" show="{{show}}" columns="{{columns}}" data-event-opts="{{[['^close',[['e1']]],['^confirm',[['confirm']]],['^cancel',[['e2']]]]}}" bind:close="__e" bind:confirm="__e" bind:cancel="__e" class="data-v-15580b72" bind:__l="__l"></u-picker></view>