# 结算状态更新说明

## 状态定义更新

根据新的业务需求，结算状态已更新为以下5种状态：

### 状态值对应关系
- `payStatus = null/undefined`: **待结算** - 工单已完成但未申请结算
- `payStatus = 0`: **待审核** - 已申请结算，等待审核
- `payStatus = 1`: **审核通过** - 结算申请已通过审核，等待付款
- `payStatus = 2`: **已付款** - 结算完成，款项已支付
- `payStatus = 3`: **审核失败** - 结算申请被拒绝，可重新申请

## Tab栏更新

页面Tab栏已更新为5个标签页：
1. **待结算** - 显示未申请结算的工单
2. **待审核** - 显示正在审核中的工单
3. **审核通过** - 显示审核通过等待付款的工单
4. **已付款** - 显示已完成付款的工单
5. **审核失败** - 显示审核失败的工单

## 功能增强

### 1. 重新申请结算
- 审核失败的工单可以重新申请结算
- 按钮文本会根据状态显示"申请结算"或"重新申请"

### 2. 状态颜色区分
- **待结算**: 橙色 (#ff9900)
- **待审核**: 蓝色 (#2d8cf0)
- **审核通过**: 绿色 (#19be6b)
- **已付款**: 深绿色 (#67c23a)
- **审核失败**: 红色 (#f56c6c)

### 3. 审核失败原因显示
- 在工单详情弹窗中，审核失败的工单会显示"失败原因"
- 失败原因使用红色背景高亮显示
- 样式包含左边框和特殊背景色

### 4. 模拟数据完善
- 为每个状态提供了对应的模拟数据
- 包含不同类型的工单和完整的字段信息
- 审核失败的工单包含失败原因说明

## 数据筛选逻辑

### API参数设置
```javascript
// 根据Tab状态设置payStatus参数
if (currentTabValue === 'pending') {
  params.payStatus = null; // 待结算
} else if (currentTabValue === 'auditing') {
  params.payStatus = 0; // 待审核
} else if (currentTabValue === 'approved') {
  params.payStatus = 1; // 审核通过
} else if (currentTabValue === 'settled') {
  params.payStatus = 2; // 已付款
} else if (currentTabValue === 'rejected') {
  params.payStatus = 3; // 审核失败
}
```

### 状态判断函数
```javascript
getSettleStatus(item) {
  if (item.payStatus === null || item.payStatus === undefined) {
    return "pending"; // 待结算
  } else if (item.payStatus === 0) {
    return "auditing"; // 待审核
  } else if (item.payStatus === 1) {
    return "approved"; // 审核通过
  } else if (item.payStatus === 2) {
    return "settled"; // 已付款
  } else if (item.payStatus === 3) {
    return "rejected"; // 审核失败
  }
  return "pending";
}
```

## 用户体验改进

1. **清晰的状态区分**: 每种状态都有独特的颜色和文本
2. **操作提示**: 按钮文本根据状态动态变化
3. **错误信息突出**: 审核失败原因使用醒目的样式显示
4. **完整的工作流**: 支持从申请到完成的完整结算流程

## 兼容性说明

- 保持了原有的API接口结构
- 向后兼容原有的数据格式
- 如果API不存在，会自动使用模拟数据
- 所有功能都有完整的错误处理机制
