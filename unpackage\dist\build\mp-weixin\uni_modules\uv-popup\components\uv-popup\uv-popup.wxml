<block wx:if="{{showPopup}}"><view class="{{['uv-popup','data-v-5d1bda6c',popupClass,isDesktop?'fixforpc-z-index':'']}}" style="{{'z-index:'+(zIndex)+';'}}"><view data-event-opts="{{[['touchstart',[['touchstart',['$event']]]]]}}" bindtouchstart="__e" class="data-v-5d1bda6c"><block wx:if="{{maskShow&&overlay}}"><uv-overlay vue-id="4bb52b96-1" show="{{showTrans}}" duration="{{duration}}" custom-style="{{overlayStyle}}" opacity="{{overlayOpacity}}" zIndex="{{zIndex}}" data-event-opts="{{[['^click',[['onTap']]]]}}" bind:click="__e" class="data-v-5d1bda6c" bind:__l="__l"></uv-overlay></block><uv-transition vue-id="4bb52b96-2" mode="{{ani}}" name="content" custom-style="{{transitionStyle}}" duration="{{duration}}" show="{{showTrans}}" data-event-opts="{{[['^click',[['onTap']]]]}}" bind:click="__e" class="data-v-5d1bda6c" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="{{['uv-popup__content','data-v-5d1bda6c',popupClass]}}" style="{{$root.s0}}" bindtap="__e"><block wx:if="{{safeAreaInsetTop}}"><uv-status-bar vue-id="{{('4bb52b96-3')+','+('4bb52b96-2')}}" class="data-v-5d1bda6c" bind:__l="__l"></uv-status-bar></block><slot></slot><block wx:if="{{safeAreaInsetBottom}}"><uv-safe-bottom vue-id="{{('4bb52b96-4')+','+('4bb52b96-2')}}" class="data-v-5d1bda6c" bind:__l="__l"></uv-safe-bottom></block><block wx:if="{{closeable}}"><view class="{{['uv-popup__content__close','data-v-5d1bda6c','uv-popup__content__close--'+closeIconPos]}}" hover-class="uv-popup__content__close--hover" hover-stay-time="150" data-event-opts="{{[['tap',[['close',['$event']]]]]}}" catchtap="__e"><uv-icon vue-id="{{('4bb52b96-5')+','+('4bb52b96-2')}}" name="close" color="#909399" size="18" bold="{{true}}" class="data-v-5d1bda6c" bind:__l="__l"></uv-icon></view></block></view></uv-transition></view></view></block>