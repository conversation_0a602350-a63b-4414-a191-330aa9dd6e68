<page-meta page-style="{{'overflow:'+(showFinishPopup||showSettlePopup?'hidden':'visible')}}" class="data-v-b8e97c1a"></page-meta><page-container show="{{showFinishPopup||showSettlePopup}}" data-event-opts="{{[['beforeleave',[['e0',['$event']]]]]}}" bindbeforeleave="__e" class="data-v-b8e97c1a"></page-container><view class="workbench-wrap data-v-b8e97c1a"><view class="search-container data-v-b8e97c1a"><view class="search-box data-v-b8e97c1a"><u-search vue-id="6c894980-1" shape="square" placeholder="搜索工单名称、客户名称、联系人" showAction="{{true}}" actionText="搜索" value="{{keyWords}}" data-event-opts="{{[['^search',[['handleSearch']]],['^custom',[['handleSearch']]],['^input',[['__set_model',['','keyWords','$event',[]]]]]]}}" bind:search="__e" bind:custom="__e" bind:input="__e" class="data-v-b8e97c1a" bind:__l="__l"></u-search></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="filter-btn data-v-b8e97c1a" bindtap="__e"><u-icon vue-id="6c894980-2" name="list" size="20" color="#666" class="data-v-b8e97c1a" bind:__l="__l"></u-icon><text class="data-v-b8e97c1a">筛选</text></view></view><view class="order-tab-bar data-v-b8e97c1a"><block wx:for="{{subList}}" wx:for-item="tab" wx:for-index="idx" wx:key="value"><view data-event-opts="{{[['tap',[['sectionChange',[idx]]]]]}}" class="{{['data-v-b8e97c1a','tab-item',[(currentStatus===idx)?'active':'']]}}" bindtap="__e">{{''+tab.label+''}}</view></block></view><u-popup vue-id="6c894980-3" show="{{showFilterPopup}}" mode="bottom" closeable="{{true}}" data-event-opts="{{[['^close',[['e2']]]]}}" bind:close="__e" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}"><view class="filter-popup data-v-b8e97c1a"><view class="popup-title data-v-b8e97c1a">筛选条件</view><view class="filter-content data-v-b8e97c1a"><view class="filter-item data-v-b8e97c1a"><view class="filter-label data-v-b8e97c1a">支付状态</view><view class="filter-options data-v-b8e97c1a"><block wx:for="{{payStatus}}" wx:for-item="status" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" data-event-params="{{({status})}}" class="{{['data-v-b8e97c1a','filter-option',[(filterForm.payStatus===status.value)?'active':'']]}}" bindtap="__e">{{''+status.label+''}}</view></block></view></view><view class="filter-item data-v-b8e97c1a"><view class="filter-label data-v-b8e97c1a">服务时间</view><view class="date-range data-v-b8e97c1a"><u-input vue-id="{{('6c894980-4')+','+('6c894980-3')}}" placeholder="开始日期" type="select" value="{{filterForm.startDate}}" data-event-opts="{{[['^click',[['e4']]],['^input',[['__set_model',['$0','startDate','$event',[]],['filterForm']]]]]}}" bind:click="__e" bind:input="__e" class="data-v-b8e97c1a" bind:__l="__l"></u-input><text style="margin:0 10rpx;" class="data-v-b8e97c1a">至</text><u-input vue-id="{{('6c894980-5')+','+('6c894980-3')}}" placeholder="结束日期" type="select" value="{{filterForm.endDate}}" data-event-opts="{{[['^click',[['e5']]],['^input',[['__set_model',['$0','endDate','$event',[]],['filterForm']]]]]}}" bind:click="__e" bind:input="__e" class="data-v-b8e97c1a" bind:__l="__l"></u-input></view></view></view><view class="filter-footer data-v-b8e97c1a"><u-button vue-id="{{('6c894980-6')+','+('6c894980-3')}}" type="info" plain="{{true}}" data-event-opts="{{[['^click',[['resetFilter']]]]}}" bind:click="__e" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}">重置</u-button><u-button vue-id="{{('6c894980-7')+','+('6c894980-3')}}" type="primary" data-event-opts="{{[['^click',[['applyFilter']]]]}}" bind:click="__e" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}">确定</u-button></view></view></u-popup><u-datetime-picker vue-id="6c894980-8" show="{{showStartDatePicker}}" mode="date" value="{{filterForm.startDate}}" data-event-opts="{{[['^cancel',[['e6']]],['^confirm',[['e7']]],['^input',[['__set_model',['$0','startDate','$event',[]],['filterForm']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-b8e97c1a" bind:__l="__l"></u-datetime-picker><u-datetime-picker vue-id="6c894980-9" show="{{showEndDatePicker}}" mode="date" value="{{filterForm.endDate}}" data-event-opts="{{[['^cancel',[['e8']]],['^confirm',[['e9']]],['^input',[['__set_model',['$0','endDate','$event',[]],['filterForm']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-b8e97c1a" bind:__l="__l"></u-datetime-picker><view class="order-list data-v-b8e97c1a"><block wx:if="{{$root.g0===0}}"><view class="empty-tip data-v-b8e97c1a">暂无工单</view></block><block wx:for="{{list}}" wx:for-item="item" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','id',item.id]]]]]]]}}" class="order-card data-v-b8e97c1a" catchtap="__e"><view class="order-header data-v-b8e97c1a"><view class="order-title data-v-b8e97c1a">{{item.objectName||"--"}}</view><view style="display:flex;gap:8rpx;" class="data-v-b8e97c1a"><view class="{{['order-status','data-v-b8e97c1a','status-'+item.objectStatus]}}">{{statusMap[item.objectStatus]}}</view><block wx:if="{{item.payStatus!==null&&item.payStatus!==undefined}}"><view class="{{['order-status','pay-status','data-v-b8e97c1a','pay-status-'+item.payStatus]}}">{{''+payStatusMap[item.payStatus]+''}}</view></block></view></view><view class="order-detail-row data-v-b8e97c1a"><view class="order-label data-v-b8e97c1a">服务类型：</view><view class="order-value data-v-b8e97c1a">{{item.serverTypeName||"--"}}</view></view><view class="order-detail-row data-v-b8e97c1a"><view class="order-label data-v-b8e97c1a">服务客户：</view><view class="order-value data-v-b8e97c1a">{{item.finalCustomer||"--"}}</view></view><view class="order-detail-row data-v-b8e97c1a"><view class="order-label data-v-b8e97c1a">联系人：</view><view class="order-value data-v-b8e97c1a">{{item.contact||"--"}}</view></view><view class="order-detail-row data-v-b8e97c1a"><view class="order-label data-v-b8e97c1a">联系电话：</view><view class="order-value data-v-b8e97c1a">{{item.contactPhone||"--"}}</view><block wx:if="{{item.contactPhone}}"><u-icon style="margin-left:10rpx;cursor:pointer;" vue-id="{{'6c894980-10-'+__i1__}}" name="phone" size="20" color="#2979ff" data-event-opts="{{[['^tap',[['makePhoneCall',['$0'],[[['list','id',item.id,'contactPhone']]]]]]]}}" catch:tap="__e" class="data-v-b8e97c1a" bind:__l="__l"></u-icon></block></view><view class="order-detail-row data-v-b8e97c1a"><view class="order-label data-v-b8e97c1a">服务地址：</view><view class="order-value data-v-b8e97c1a">{{item.distributionAddress||"--"}}</view></view><view class="order-detail-row data-v-b8e97c1a"><view class="order-label data-v-b8e97c1a">服务时间：</view><view class="order-value data-v-b8e97c1a">{{(item.serviceStartTime||"--")+" - "+(item.serviceEndTime||"--")}}</view></view><block wx:if="{{item.orderPrice}}"><view class="order-detail-row data-v-b8e97c1a"><view class="order-label data-v-b8e97c1a">金额：</view><view class="order-value price data-v-b8e97c1a">{{"¥"+item.orderPrice}}</view></view></block><view class="order-actions data-v-b8e97c1a"><block wx:if="{{item.objectStatus==3}}"><u-button class="action-btn primary data-v-b8e97c1a" vue-id="{{'6c894980-11-'+__i1__}}" type="primary" plain="{{true}}" data-event-opts="{{[['^tap',[['handleStart',['$0'],[[['list','id',item.id]]]]]]]}}" catch:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">接单</u-button></block><block wx:else><block wx:if="{{item.objectStatus==1}}"><u-button class="action-btn primary data-v-b8e97c1a" vue-id="{{'6c894980-12-'+__i1__}}" plain="{{true}}" type="primary" data-event-opts="{{[['^tap',[['handleFinish',['$0'],[[['list','id',item.id]]]]]]]}}" catch:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">完成</u-button></block><block wx:else><block wx:if="{{item.objectStatus==2&&item.payStatus==null}}"><u-button class="action-btn primary data-v-b8e97c1a" vue-id="{{'6c894980-13-'+__i1__}}" plain="{{true}}" type="primary" data-event-opts="{{[['^tap',[['handleSettled',['$0'],[[['list','id',item.id]]]]]]]}}" catch:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">申请结算</u-button></block></block></block></view></view></block><uni-load-more vue-id="6c894980-14" status="{{$root.g1==page.total?'noMore':'loading'}}" class="data-v-b8e97c1a" bind:__l="__l"></uni-load-more></view><u-modal vue-id="6c894980-15" show="{{modalShow}}" title="确认接单" content="确认接单吗？" showCancelButton="{{true}}" asyncClose="{{true}}" data-ref="uModal" data-event-opts="{{[['^confirm',[['confirm']]],['^cancel',[['e10']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-b8e97c1a vue-ref" bind:__l="__l"></u-modal><u-modal vue-id="6c894980-16" show="{{show}}" content="你还未完善手机号,姓名等信息,请点击确认去完善" data-event-opts="{{[['^confirm',[['toUserInfo']]]]}}" bind:confirm="__e" class="data-v-b8e97c1a" bind:__l="__l"></u-modal><u-popup vue-id="6c894980-17" show="{{showFinishPopup}}" mode="bottom" closeable="{{true}}" data-event-opts="{{[['^close',[['e11']]]]}}" bind:close="__e" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}"><view class="finish-popup data-v-b8e97c1a"><view class="popup-title data-v-b8e97c1a">完成工单</view><view class="popup-content data-v-b8e97c1a"><u-form vue-id="{{('6c894980-18')+','+('6c894980-17')}}" labelPosition="top" labelWidth="auto" model="{{finishForm}}" data-ref="finishForm" class="data-v-b8e97c1a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('6c894980-19')+','+('6c894980-18')}}" borderBottom="{{true}}" labelPosition="left" label="完成时间" required="{{true}}" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}"><view style="display:flex;justify-content:flex-end;align-items:center;" class="data-v-b8e97c1a"><text data-event-opts="{{[['tap',[['e12',['$event']]]]]}}" bindtap="__e" class="data-v-b8e97c1a">{{$root.m0+''}}<u-icon vue-id="{{('6c894980-20')+','+('6c894980-19')}}" label="uView" size="40" name="arrow-right" class="data-v-b8e97c1a" bind:__l="__l"></u-icon></text></view><u-datetime-picker vue-id="{{('6c894980-21')+','+('6c894980-19')}}" show="{{showFinishTimePicker}}" mode="datetime" visibleItemCount="{{5}}" value="{{finishForm.finishTime}}" data-event-opts="{{[['^cancel',[['e13']]],['^confirm',[['e14']]],['^input',[['__set_model',['$0','finishTime','$event',[]],['finishForm']]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" class="data-v-b8e97c1a" bind:__l="__l"></u-datetime-picker></u-form-item><u-form-item vue-id="{{('6c894980-22')+','+('6c894980-18')}}" borderBottom="{{true}}" label="备注" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('6c894980-23')+','+('6c894980-22')}}" border="none" placeholder="请输入备注信息" value="{{finishForm.remark}}" data-event-opts="{{[['^input',[['__set_model',['$0','remark','$event',[]],['finishForm']]]]]}}" class="data-v-b8e97c1a" bind:__l="__l"></u-textarea></u-form-item><u-form-item vue-id="{{('6c894980-24')+','+('6c894980-18')}}" borderBottom="{{true}}" label="上传图片/视频" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}"><uv-upload vue-id="{{('6c894980-25')+','+('6c894980-24')}}" accept="media" fileList="{{finishForm.fileList}}" multiple="{{true}}" maxCount="{{9}}" data-event-opts="{{[['^clickPreview',[['handleClickPreview']]],['^afterRead',[['afterRead']]],['^delete',[['handleDelete']]]]}}" bind:clickPreview="__e" bind:afterRead="__e" bind:delete="__e" class="data-v-b8e97c1a" bind:__l="__l"></uv-upload></u-form-item></u-form></view><view class="popup-footer data-v-b8e97c1a"><u-button vue-id="{{('6c894980-26')+','+('6c894980-17')}}" type="primary" data-event-opts="{{[['^click',[['submitFinish']]]]}}" bind:click="__e" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}">提交</u-button></view></view></u-popup><u-popup vue-id="6c894980-27" show="{{showSettlePopup}}" mode="bottom" closeable="{{true}}" data-event-opts="{{[['^close',[['e15']]]]}}" bind:close="__e" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}"><view class="finish-popup data-v-b8e97c1a"><view class="popup-title data-v-b8e97c1a">申请结算</view><view class="popup-content data-v-b8e97c1a"><u-form vue-id="{{('6c894980-28')+','+('6c894980-27')}}" labelPosition="top" labelWidth="auto" model="{{settleForm}}" data-ref="settleForm" class="data-v-b8e97c1a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('6c894980-29')+','+('6c894980-28')}}" borderBottom="{{true}}" label="结算金额" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}"><u--input bind:input="__e" vue-id="{{('6c894980-30')+','+('6c894980-29')}}" placeholder="请输入结算金额" border="none" type="number" value="{{settleForm.totalPrice}}" data-event-opts="{{[['^input',[['__set_model',['$0','totalPrice','$event',[]],['settleForm']]]]]}}" class="data-v-b8e97c1a" bind:__l="__l"></u--input></u-form-item><u-form-item vue-id="{{('6c894980-31')+','+('6c894980-28')}}" borderBottom="{{true}}" label="备注" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('6c894980-32')+','+('6c894980-31')}}" border="none" placeholder="请输入备注信息" value="{{settleForm.applyContent}}" data-event-opts="{{[['^input',[['__set_model',['$0','applyContent','$event',[]],['settleForm']]]]]}}" class="data-v-b8e97c1a" bind:__l="__l"></u-textarea></u-form-item></u-form></view><view class="popup-footer data-v-b8e97c1a"><u-button vue-id="{{('6c894980-33')+','+('6c894980-27')}}" type="primary" data-event-opts="{{[['^click',[['submitSettle']]]]}}" bind:click="__e" class="data-v-b8e97c1a" bind:__l="__l" vue-slots="{{['default']}}">提交</u-button></view></view></u-popup></view>