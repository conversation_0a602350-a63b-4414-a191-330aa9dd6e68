(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/wokerOrder/wokerOrder"],{"0a1e":function(t,e,n){"use strict";n.r(e);var i=n("cdd2"),o=n("c86c");for(var u in o)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(u);n("5099");var r=n("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"b8e97c1a",null,!1,i["a"],void 0);e["default"]=s.exports},"22ba":function(t,e,n){"use strict";(function(t){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("7ca3")),u=i(n("af34")),r=n("389b"),s=i(n("0814"));function a(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function l(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?a(Object(n),!0).forEach((function(e){(0,o.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var c={name:"workerOrder",data:function(){return{keyWords:"",currentStatus:0,dateFormat:r.dateFormat,list:[],showFinishPopup:!1,showFinishTimePicker:!1,modalShow:!1,finishForm:{finishTime:Number(new Date),remark:"",fileList:[]},page:{size:10,current:1,total:0},subList:[{label:"待接单",value:3},{label:"进行中",value:1},{label:"已完成",value:2}],payStatus:[{value:0,label:"待审核"},{value:1,label:"待付款"},{value:2,label:"已付款"},{value:3,label:"审核失败"}],show:!1,showSettlePopup:!1,settleForm:{},summary:{totalNum:0,waitNum:0,doNum:0,completeNum:0},statusMap:{3:"待接单",1:"进行中",2:"已完成"},payStatusMap:{0:"待审核",1:"待付款",2:"已付款",3:"审核失败"},showFilterPopup:!1,showStartDatePicker:!1,showEndDatePicker:!1,filterForm:{payStatus:null,startDate:"",endDate:""}}},onReady:function(){this.getList(),this.getTaskStatistics()},onShow:function(){this.validatePhone()},onPullDownRefresh:function(){this.refreshData()},onReachBottom:function(){this.scrolltolower()},methods:{getTaskStatistics:function(){var t=this;this.$u.api.getTaskStatistics().then((function(e){t.summary=e.data||{}})).catch((function(){t.summary={totalNum:0,waitNum:0,doNum:0,completeNum:0}}))},refreshData:function(){this.page.current=1,this.list=[],this.getList(),this.getTaskStatistics(),t.stopPullDownRefresh()},getList:function(){var t=this,e={size:this.page.size,current:this.page.current,objectStatus:this.subList[this.currentStatus].value,objectName:this.keyWords};null!==this.filterForm.payStatus&&(e.payStatus=this.filterForm.payStatus),this.filterForm.startDate&&(e.startDate=this.filterForm.startDate),this.filterForm.endDate&&(e.endDate=this.filterForm.endDate),this.$u.api.getWorkerOrder(e).then((function(e){t.list=[].concat((0,u.default)(t.list),(0,u.default)(e.data.records)),t.page.total=e.data.total}))},scrolltolower:function(){console.log(1111),this.list.length!=this.page.total&&(this.page.current++,this.getList())},sectionChange:function(t){this.currentStatus=t,this.page.current=1,this.list=[],this.getList()},handleSearch:function(){this.list=[],this.page.current=1,this.getList()},handleStart:function(t){this.currentItem=t,this.modalShow=!0},confirm:function(){var t=this;this.$u.api.startWorkerOrder(this.currentItem.id).then((function(e){t.modalShow=!1,t.page.current=1,t.list=[],t.getList()}))},handleFinish:function(t){this.showFinishPopup=!0,this.currentItem=t},afterRead:function(t){var e=this;console.log(t);var n=t.file,i=this.finishForm.fileList.length;n.forEach((function(t,n){e.finishForm.fileList.push(l(l({},t),{},{status:"uploading",message:"上传中",index:i+n}))})),n.forEach((function(t,n){e.uploadFile(t.url,i+n)}))},uploadFile:function(t,e){var n=this;return new Promise((function(i){var o={filePath:t,name:"file"};s.default.upload("/blade-resource/attach/upload",o).then((function(t){n.finishForm.fileList.find((function(t){return t.index==e})).status="success",n.finishForm.fileList.find((function(t){return t.index==e})).message="",n.finishForm.fileList.find((function(t){return t.index==e})).url=t.data.link,n.finishForm.fileList.find((function(t){return t.index==e})).id=t.data.id}))}))},handleDelete:function(t){var e=t.file,n=t.index,i=t.name;console.log(e,n,i),this.finishForm.fileList.splice(n,1)},handleClickPreview:function(t,e,n){console.log(t,e,n)},submitFinish:function(){var t=this;if(this.finishForm.finishTime){var e={id:this.currentItem.id,finishTime:this.dateFormat(new Date(Number(this.finishForm.finishTime)),"yyyy-MM-dd hh:mm:ss"),completeRemark:this.finishForm.remark,completeFiles:this.finishForm.fileList&&this.finishForm.fileList.map((function(t){return t.id})).join(",")};this.$u.api.finishWorkerOrder(e).then((function(e){t.$u.toast("提交成功"),t.showFinishPopup=!1,t.finishForm={finishTime:"",remark:"",fileList:[]},t.getList()})).catch((function(e){t.$u.toast(e.message||"提交失败")}))}else this.$u.toast("请选择完成时间")},toDetail:function(e){t.navigateTo({url:"/pages/wokerOrder/wokerOrderDetail?id="+e.id})},validatePhone:function(){var t=this;this.$u.api.userInfo().then((function(e){e.data.phone?(console.log(e),t.show=!1):t.show=!0}))},toUserInfo:function(){t.navigateTo({url:"/pages/person/userInfo"})},handleSettled:function(t){this.showSettlePopup=!0,this.settleForm={objectId:t.id,totalPrice:t.orderPrice}},submitSettle:function(){var t=this;this.$u.api.applySettlement(this.settleForm).then((function(e){t.$u.toast("申请成功"),t.showSettlePopup=!1,t.settleForm={objectId:"",totalPrice:""},t.list=[],t.page.current=1,t.getList()}))},resetFilter:function(){this.filterForm={payStatus:null,startDate:"",endDate:""}},applyFilter:function(){this.showFilterPopup=!1,this.page.current=1,this.list=[],this.getList()},makePhoneCall:function(e){t.makePhoneCall({phoneNumber:e,success:function(){console.log("拨打电话成功！")},fail:function(t){console.log("拨打电话失败！",t)}})}}};e.default=c}).call(this,n("df3c")["default"])},"434e":function(t,e,n){},5099:function(t,e,n){"use strict";var i=n("434e"),o=n.n(i);o.a},c86c:function(t,e,n){"use strict";n.r(e);var i=n("22ba"),o=n.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(u);e["default"]=o.a},cbec:function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("ea4a");i(n("3240"));var o=i(n("0a1e"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},cdd2:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return u})),n.d(e,"a",(function(){return i}));var i={uSearch:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-search/u-search")]).then(n.bind(null,"716b"))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(null,"073f"))},uPopup:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-popup/u-popup")]).then(n.bind(null,"1a2c"))},uInput:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-input/u-input")]).then(n.bind(null,"4a78"))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-button/u-button")]).then(n.bind(null,"f8f8"))},uDatetimePicker:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker")]).then(n.bind(null,"d33a"))},uniLoadMore:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-load-more/components/uni-load-more/uni-load-more")]).then(n.bind(null,"4357"))},uModal:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-modal/u-modal")]).then(n.bind(null,"b5ea"))},uForm:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-form/u-form")]).then(n.bind(null,"a7f1"))},uFormItem:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-form-item/u-form-item")]).then(n.bind(null,"1da9"))},uTextarea:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-textarea/u-textarea")]).then(n.bind(null,"6704"))},uvUpload:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uv-upload/components/uv-upload/uv-upload")]).then(n.bind(null,"ddd1"))},"u-Input":function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u--input/u--input")]).then(n.bind(null,"5799"))}},o=function(){var t=this,e=t.$createElement,n=(t._self._c,t.list.length),i=t.list.length,o=t.dateFormat(new Date(Number(t.finishForm.finishTime)),"yyyy-MM-dd hh:mm")||"请选择完成时间";t._isMounted||(t.e0=function(e){t.showFinishPopup=!1,t.showSettlePopup=!1},t.e1=function(e){t.showFilterPopup=!0},t.e2=function(e){t.showFilterPopup=!1},t.e3=function(e,n){var i=arguments[arguments.length-1].currentTarget.dataset,o=i.eventParams||i["event-params"];n=o.status;t.filterForm.payStatus=t.filterForm.payStatus===n.value?null:n.value},t.e4=function(e){t.showStartDatePicker=!0},t.e5=function(e){t.showEndDatePicker=!0},t.e6=function(e){t.showStartDatePicker=!1},t.e7=function(e){t.showStartDatePicker=!1},t.e8=function(e){t.showEndDatePicker=!1},t.e9=function(e){t.showEndDatePicker=!1},t.e10=function(e){t.modalShow=!1},t.e11=function(e){t.showFinishPopup=!1},t.e12=function(e){t.showFinishTimePicker=!0},t.e13=function(e){t.showFinishTimePicker=!1},t.e14=function(e){t.showFinishTimePicker=!1},t.e15=function(e){t.showSettlePopup=!1}),t.$mp.data=Object.assign({},{$root:{g0:n,g1:i,m0:o}})},u=[]}},[["cbec","common/runtime","common/vendor"]]]);