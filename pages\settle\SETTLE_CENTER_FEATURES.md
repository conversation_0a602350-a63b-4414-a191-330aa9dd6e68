# 结算中心页面功能说明

## 页面概述
结算中心页面用于管理和查看工单的结算情况，包括待结算、结算中和已结算的工单。

## 主要功能

### 1. 结算金额统计
- **全部金额**: 显示所有工单的总金额
- **待结算**: 显示待结算工单的总金额
- **结算中**: 显示正在结算中的工单总金额  
- **已结算**: 显示已完成结算的工单总金额

### 2. 工单分类查看
- **待结算**: 已完成但未申请结算的工单
- **结算中**: 已申请结算但未完成付款的工单
- **已结算**: 已完成结算和付款的工单

### 3. 搜索和筛选功能
- **搜索**: 支持按工单名称、客户名称、联系人搜索
- **金额筛选**: 支持按金额范围筛选
- **时间筛选**: 支持按结算时间范围筛选

### 4. 工单信息展示
每个工单卡片显示以下信息：
- 工单名称和状态标签
- 服务类型
- 服务客户
- 联系人和电话（支持一键拨打）
- 服务地址
- 完成时间
- 工单金额（红色显示）
- 结算金额（绿色显示，如果已结算）
- 结算时间（如果已结算）

### 5. 操作功能
- **申请结算**: 对待结算的工单可以申请结算
- **查看详情**: 点击工单卡片查看详细信息
- **电话拨打**: 点击电话号码直接拨打

### 6. 状态管理
- **工单状态**: 显示工单的完成状态
- **结算状态**: 
  - 待结算（橙色）
  - 结算中（蓝色）
  - 已结算（绿色）

## 数据逻辑

### 结算状态判断
```javascript
// 根据 payStatus 判断结算状态
- payStatus === null/undefined: 待结算
- payStatus === 0/1: 结算中（待审核/待付款）
- payStatus === 2: 已结算
```

### 工单筛选条件
- 只显示已完成的工单（objectStatus === 2）
- 根据当前Tab筛选不同结算状态的工单
- 支持关键词搜索和高级筛选

## 界面设计特点

### 1. 现代化设计
- 渐变背景
- 圆角卡片设计
- 阴影效果
- 响应式布局

### 2. 颜色系统
- 蓝色：主色调，用于按钮和链接
- 橙色：待处理状态
- 绿色：成功/完成状态
- 红色：金额显示
- 灰色：次要信息

### 3. 交互体验
- 下拉刷新
- 上拉加载更多
- 弹窗详情展示
- 一键操作

## API 接口需求

### 1. 获取结算统计
```javascript
this.$u.api.getSettleSummary()
```

### 2. 获取结算工单列表
```javascript
this.$u.api.getSettleOrders(params)
```

### 3. 申请结算
```javascript
this.$u.api.applySettlement(settleForm)
```

## 模拟数据
页面包含完整的模拟数据，即使API接口不存在也能正常展示功能。

## 技术特点
- Vue.js 组件化开发
- SCSS 样式预处理
- 响应式设计
- 错误处理和降级方案
- 代码结构清晰，易于维护
