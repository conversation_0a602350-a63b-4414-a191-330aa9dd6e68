(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/wokerOrder/wokerOrderDetail"],{1362:function(e,t,i){"use strict";i.r(t);var n=i("26a7"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"26a7":function(e,t,i){"use strict";(function(e,n){var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(i("7ca3")),s=o(i("4d20")),a=o(i("0814")),l=i("389b");function u(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function c(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?u(Object(i),!0).forEach((function(t){(0,r.default)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):u(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var d={name:"WokerOrderDetail",components:{dicPicker:function(){i.e("components/dic-picker/dic-picker").then(function(){return resolve(i("7ed6"))}.bind(null,i)).catch(i.oe)}},data:function(){return{detailForm:{objectStatus:2,customerName:"测试客户公司",contact:"张经理",contactPhone:"13800138000",distributionAddress:"北京市朝阳区测试大厦10层",objectName:"服务器维护工单",serverTypeName:"硬件维护",serviceStartTime:"2024-01-15 09:00:00",serviceEndTime:"2024-01-15 18:00:00",projectLeaderName:"项目经理",handleUserName:"张工程师",SLATypeName:"标准SLA",taskDescription:"对服务器进行全面检查和维护，确保系统稳定运行。",fileList:[],milestoneVOList:[{handleContent:"工单已创建",createTime:"2024-01-14 10:00:00"},{handleContent:"工程师已接单",createTime:"2024-01-14 14:30:00"},{handleContent:"现场签到完成",createTime:"2024-01-15 09:00:00"},{handleContent:"工单已完成",createTime:"2024-01-15 18:00:00"}],sealContractObjectResultVOList:[{handleName:"张工程师",serviceStartTime:"2024-01-15 09:00:00",serviceEndTime:"2024-01-15 17:30:00",useTimes:8.5,completeStatus:1,completeStatusName:"完全完成",signTime:"2024-01-15 08:55:00",signAddress:"北京市朝阳区测试大厦10层",serviceReorder:"本次服务顺利完成，客户设备运行正常。在服务过程中发现了一些潜在问题并及时处理，建议客户定期进行设备维护检查。",completeRemark:"设备已恢复正常运行，客户满意度较高。所有问题已解决，系统运行稳定。",signPhotoList:[{link:"https://via.placeholder.com/300x200/673AB7/white?text=签到图1"},{link:"https://via.placeholder.com/300x200/3F51B5/white?text=签到图2"}],workOrderPhotoList:[{link:"https://via.placeholder.com/300x200/4CAF50/white?text=现场图1"},{link:"https://via.placeholder.com/300x200/2196F3/white?text=现场图2"},{link:"https://via.placeholder.com/300x200/FF9800/white?text=现场图3"}],handleResultPhotoList:[{link:"https://via.placeholder.com/300x200/9C27B0/white?text=结果图1"},{link:"https://via.placeholder.com/300x200/F44336/white?text=结果图2"}]},{handleName:"李技术员",serviceStartTime:"2024-01-16 14:00:00",serviceEndTime:"2024-01-16 16:00:00",useTimes:2,completeStatus:2,completeStatusName:"部分完成",signTime:"2024-01-16 13:55:00",signAddress:"北京市朝阳区测试大厦10层",serviceReorder:"协助主工程师完成设备调试工作，负责数据备份和系统配置。",completeRemark:"辅助工作完成良好，配合主工程师顺利完成工单。",signPhotoList:[{link:"https://via.placeholder.com/300x200/795548/white?text=李工签到"}],workOrderPhotoList:[{link:"https://via.placeholder.com/300x200/607D8B/white?text=辅助现场图"}],handleResultPhotoList:[{link:"https://via.placeholder.com/300x200/795548/white?text=辅助结果图"}]}]},dateFormat:l.dateFormat,report:{handleUserName:"张工程师",serviceStartTime:"2024-01-15 09:00:00",serviceEndTime:"2024-01-15 17:30:00",useTimes:8.5,completeStatus:1,serviceReorder:"本次服务顺利完成，客户设备运行正常。在服务过程中发现了一些潜在问题并及时处理，建议客户定期进行设备维护检查。具体完成内容包括：\n1. 服务器硬件检查\n2. 系统性能优化\n3. 安全补丁更新\n4. 数据备份验证",completeRemark:"设备已恢复正常运行，客户满意度较高。所有问题已解决，系统运行稳定。",workOrderPhotos:[{url:"https://via.placeholder.com/300x200/4CAF50/white?text=现场图1"},{url:"https://via.placeholder.com/300x200/2196F3/white?text=现场图2"},{url:"https://via.placeholder.com/300x200/FF9800/white?text=现场图3"}],handleResultPhotos:[{url:"https://via.placeholder.com/300x200/9C27B0/white?text=结果图1"},{url:"https://via.placeholder.com/300x200/F44336/white?text=结果图2"}]},showSignDrawer:!1,locationLoading:!1,signAddress:null,signPhotoUrl:[],signRemark:"",qqmapsdk:null,acceptOrderModalShow:!1,showFinishPopup:!1,shiwServiceStartTimePicker:!1,shiwServiceEndTimePicker:!1,finishForm:{serviceStartTime:Number(new Date),serviceEndTime:Number(new Date),completeRemark:"",useTimes:null,serviceReorder:"",workOrderPhotos:[],handleResultPhotos:[],completeStatus:null},expandedSections:{}}},computed:{showActionButtons:function(){return!!this.detailForm.objectStatus&&(3==this.detailForm.objectStatus||1==this.detailForm.objectStatus)},sealContractObjectResultVOList:function(){return this.detailForm.sealContractObjectResultVOList||[]}},onLoad:function(e){var t=e.id;t&&this.getWorkerOrderDetail(t),this.qqmapsdk=new s.default({key:"V37BZ-5JF63-HBZ3S-34XAJ-KPG2Q-74FPM"})},methods:{getWorkerOrderDetail:function(e){var t=this;this.$u.api.getWorkerOrderDetail(e).then((function(e){t.detailForm=e.data,t.detailForm.completeFileList&&(t.detailForm.completeFileList=t.detailForm.completeFileList.map((function(e){return c(c({},e),{},{url:e.link})}))),console.log(t.detailForm)}))},callPhone:function(t){e.makePhoneCall({phoneNumber:t,success:function(){console.log("拨打电话成功！")},fail:function(e){console.log("拨打电话失败！",e)}})},handleAccept:function(){this.acceptOrderModalShow=!0},acceptOrderConfirm:function(){var e=this;this.$u.api.startWorkerOrder(this.detailForm.id).then((function(t){e.acceptOrderModalShow=!1,e.$u.toast("接单成功"),e.getWorkerOrderDetail(e.detailForm.id)})).catch((function(t){e.$u.toast(t.message||"接单失败")}))},handleSign:function(){this.showSignDrawer=!0,this.signAddress=null,this.signPhotoUrl=[],this.signRemark="",this.chooseLocation()},chooseLocation:function(){var t=this;this.locationLoading=!0,n.getLocation({type:"gcj02",success:function(i){console.log(i),t.qqmapsdk.reverseGeocoder({location:{latitude:i.latitude,longitude:i.longitude},success:function(e){console.log("逆地址解析结果：",e);var i=e.result.address_component,n=e.result.address;console.log("所在城市：",i.city),console.log("完整地址：",n),t.locationLoading=!1,t.signAddress=n},fail:function(i){t.locationLoading=!1,console.error("逆地址解析失败：",i),e.showToast({title:"位置解析失败",icon:"none"})}})},fail:function(i){console.log(i),t.locationLoading=!1,e.showToast({title:"位置选择失败",icon:"none"})}})},afterReadSign:function(e){var t=this;console.log(e);var i=e.file,n=this.signPhotoUrl.length;i.forEach((function(e,i){t.signPhotoUrl.push(c(c({},e),{},{status:"uploading",message:"上传中",index:n+i}))})),i.forEach((function(e,i){t.uploadFileSign(e.url,n+i)}))},uploadFileSign:function(e,t){var i=this;return new Promise((function(n){var o={filePath:e,name:"file"};a.default.upload("/blade-resource/attach/upload",o).then((function(e){i.signPhotoUrl.find((function(e){return e.index==t})).status="success",i.signPhotoUrl.find((function(e){return e.index==t})).message="",i.signPhotoUrl.find((function(e){return e.index==t})).url=e.data.link,n()}))}))},handleDeleteSign:function(e){var t=e.file,i=e.index,n=e.name;console.log(t,i,n),this.signPhotoUrl.splice(i,1)},handleClickPreview:function(e,t,i){console.log(e,t,i)},submitSign:function(){var t=this;if(this.signPhotoUrl&&0!==this.signPhotoUrl.length){var i={id:this.detailForm.id,address:this.signAddress,signPhotoUrl:this.signPhotoUrl.map((function(e){return e.url})).join(",")};this.$u.api.signIn(i).then((function(i){console.log(i),e.showToast({title:"签到成功",icon:"success"}),t.showSignDrawer=!1,t.getWorkerOrderDetail(t.detailForm.id)})).catch((function(e){t.$u.toast(e.message||"签到失败")}))}else e.showToast({title:"请上传照片",icon:"none"})},handleFinish:function(){this.showFinishPopup=!0,this.finishForm.serviceStartTime=Number(new Date(this.detailForm.serviceStartTime)),this.finishForm.serviceEndTime=Number(new Date(this.detailForm.serviceEndTime))},afterReadForXC:function(e){var t=this;console.log(e);var i=e.file,n=this.finishForm.workOrderPhotos.length;i.forEach((function(e,i){t.finishForm.workOrderPhotos.push(c(c({},e),{},{status:"uploading",message:"上传中",index:n+i}))})),i.forEach((function(e,i){t.uploadFileForXC(e.url,n+i)}))},uploadFileForXC:function(e,t){var i=this;return new Promise((function(n){var o={filePath:e,name:"file"};a.default.upload("/blade-resource/attach/upload",o).then((function(e){i.finishForm.workOrderPhotos.find((function(e){return e.index==t})).status="success",i.finishForm.workOrderPhotos.find((function(e){return e.index==t})).message="",i.finishForm.workOrderPhotos.find((function(e){return e.index==t})).url=e.data.link,i.finishForm.workOrderPhotos.find((function(e){return e.index==t})).id=e.data.id,n()}))}))},handleDeleteForXC:function(e){var t=e.file,i=e.index,n=e.name;console.log(t,i,n),this.finishForm.workOrderPhotos.splice(i,1)},afterReadForFinish:function(e){var t=this;console.log(e);var i=e.file,n=this.finishForm.handleResultPhotos.length;i.forEach((function(e,i){t.finishForm.handleResultPhotos.push(c(c({},e),{},{status:"uploading",message:"上传中",index:n+i}))})),i.forEach((function(e,i){t.uploadFileForFinish(e.url,n+i)}))},uploadFileForFinish:function(e,t){var i=this;return new Promise((function(n){var o={filePath:e,name:"file"};a.default.upload("/blade-resource/attach/upload",o).then((function(e){i.finishForm.handleResultPhotos.find((function(e){return e.index==t})).status="success",i.finishForm.handleResultPhotos.find((function(e){return e.index==t})).message="",i.finishForm.handleResultPhotos.find((function(e){return e.index==t})).url=e.data.link,i.finishForm.handleResultPhotos.find((function(e){return e.index==t})).id=e.data.id,n()}))}))},handleDeleteForFinish:function(e){var t=e.file,i=e.index,n=e.name;console.log(t,i,n),this.finishForm.handleResultPhotos.splice(i,1)},submitFinish:function(){var e=this;if(this.finishForm.serviceStartTime&&this.finishForm.serviceEndTime){var t={id:this.detailForm.id,serviceStartTime:this.dateFormat(new Date(Number(this.finishForm.serviceStartTime)),"yyyy-MM-dd hh:mm:ss"),serviceEndTime:this.dateFormat(new Date(Number(this.finishForm.serviceEndTime)),"yyyy-MM-dd hh:mm:ss"),completeRemark:this.finishForm.completeRemark,handleResultPhotos:this.finishForm.handleResultPhotos&&this.finishForm.handleResultPhotos.map((function(e){return e.id})).join(","),workOrderPhotos:this.finishForm.workOrderPhotos&&this.finishForm.workOrderPhotos.map((function(e){return e.id})).join(","),useTimes:this.finishForm.useTimes,completeStatus:this.finishForm.completeStatus,serviceReorder:this.finishForm.serviceReorder};this.$u.api.finishWorkerOrder(t).then((function(t){e.$u.toast("提交成功"),e.showFinishPopup=!1,e.finishForm={serviceStartTime:Number(new Date),serviceEndTime:Number(new Date),completeRemark:"",useTimes:null,serviceReorder:"",workOrderPhotos:[],handleResultPhotos:[],completeStatus:null},e.getWorkerOrderDetail(e.detailForm.id)})).catch((function(t){e.$u.toast(t.message||"提交失败")}))}else this.$u.toast("开始时间和结束时间不能为空")},getCompleteStatusText:function(e){return{1:"完全完成",2:"部分完成",3:"未完成"}[e]||"未知状态"},getCompleteStatusClass:function(e){return{1:"status-complete",2:"status-partial",3:"status-incomplete"}[e]||""},previewImage:function(t,i){var n=i.map((function(e){return e.link||e.url}));e.previewImage({current:t,urls:n})},toggleSection:function(e,t){var i="".concat(e,"_").concat(t);this.$set(this.expandedSections,i,!this.expandedSections[i])},getExpandedState:function(e,t){var i="".concat(e,"_").concat(t);return void 0===this.expandedSections[i]?"complete"===e:this.expandedSections[i]}}};t.default=d}).call(this,i("df3c")["default"],i("3223")["default"])},5577:function(e,t,i){},c59d:function(e,t,i){"use strict";(function(e,t){var n=i("47a9");i("ea4a");n(i("3240"));var o=n(i("e695"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},de41:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uniIcons:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(i.bind(null,"53bf"))},uIcon:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(i.bind(null,"073f"))},uButton:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uview-ui/components/u-button/u-button")]).then(i.bind(null,"f8f8"))},uModal:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uview-ui/components/u-modal/u-modal")]).then(i.bind(null,"b5ea"))},uPopup:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uview-ui/components/u-popup/u-popup")]).then(i.bind(null,"1a2c"))},uvUpload:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uv-upload/components/uv-upload/uv-upload")]).then(i.bind(null,"ddd1"))},uForm:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uview-ui/components/u-form/u-form")]).then(i.bind(null,"a7f1"))},uFormItem:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uview-ui/components/u-form-item/u-form-item")]).then(i.bind(null,"1da9"))},uDatetimePicker:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker")]).then(i.bind(null,"d33a"))},uInput:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uview-ui/components/u-input/u-input")]).then(i.bind(null,"4a78"))},uTextarea:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uview-ui/components/u-textarea/u-textarea")]).then(i.bind(null,"6704"))}},o=function(){var e=this,t=e.$createElement,i=(e._self._c,2==e.detailForm.objectStatus||3==e.detailForm.objectStatus?e.__map(e.detailForm.sealContractObjectResultVOList,(function(t,i){var n=e.__get_orig(t),o=1==e.detailForm.isNeedSign?e.getExpandedState("signin",i):null,r=1==e.detailForm.isNeedSign?e.getExpandedState("signin",i):null,s=1==e.detailForm.isNeedSign?t.signPhotoUrl&&t.signPhotoUrl.length>0:null,a=1==e.detailForm.isNeedSign&&s?t.signPhotoUrl.split(","):null,l=e.getExpandedState("complete",i),u=e.getExpandedState("complete",i),c=t.workOrderPhotoList&&t.workOrderPhotoList.length>0,d=t.handleResultPhotoList&&t.handleResultPhotoList.length>0,h=e.detailForm.sealContractObjectResultVOList.length;return{$orig:n,m0:o,m1:r,g0:s,l0:a,m2:l,m3:u,g1:c,g2:d,g3:h}})):null),n=e.dateFormat(new Date(Number(e.finishForm.serviceStartTime)),"yyyy-MM-dd hh:mm")||"请选择服务开始时间",o=e.dateFormat(new Date(Number(e.finishForm.serviceEndTime)),"yyyy-MM-dd hh:mm")||"请选择服务结束时间";e._isMounted||(e.e0=function(t){e.showFinishPopup=!1,e.showSignDrawer=!1},e.e1=function(t,i,n){var o=arguments[arguments.length-1].currentTarget.dataset,r=o.eventParams||o["event-params"];i=r.photo,n=r.report;e.previewImage(i,n.signPhotoUrl.split(",").map((function(e){return{link:e}})))},e.e2=function(t){e.acceptOrderModalShow=!1},e.e3=function(t){e.showSignDrawer=!1},e.e4=function(t){e.showFinishPopup=!1},e.e5=function(t){e.shiwServiceStartTimePicker=!0},e.e6=function(t){e.shiwServiceStartTimePicker=!1},e.e7=function(t){e.shiwServiceStartTimePicker=!1},e.e8=function(t){e.shiwServiceEndTimePicker=!0},e.e9=function(t){e.shiwServiceEndTimePicker=!1},e.e10=function(t){e.shiwServiceEndTimePicker=!1}),e.$mp.data=Object.assign({},{$root:{l1:i,m4:n,m5:o}})},r=[]},e695:function(e,t,i){"use strict";i.r(t);var n=i("de41"),o=i("1362");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("edff");var s=i("828b"),a=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,"2138eec6",null,!1,n["a"],void 0);t["default"]=a.exports},edff:function(e,t,i){"use strict";var n=i("5577"),o=i.n(n);o.a}},[["c59d","common/runtime","common/vendor"]]]);